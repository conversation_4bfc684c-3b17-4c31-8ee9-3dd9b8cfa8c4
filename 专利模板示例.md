# (19)国家知识产权局

# (12)发明专利申请

(10)申请公布号CN118538330A(43)申请公布日2024.08.23

(21)申请号202410671355.7

(22)申请日2024.05.28

(71)申请人中国科学院计算机网络信息中心地址100083北京市海淀区东升南路2号院中科院计算机网络信息中心

(72)发明人张宝花 许黄超 刘倩 马英晋 李天颜 金钟

(74)专利代理机构北京亿腾知识产权代理事务所（普通合伙）11309

专利代理师陈霁

(51)Int.Cl.

G16C20/90(2019.01) G06F16/245(2019.01) G06F16/25(2019.01) G06F16/28(2019.01)

G06F16/2457(2019.01) G06N5/022(2023.01)

权利要求书2页 说明书18页 附图3页

# (54)发明名称

一种材料智慧专家系统

# (57)摘要

本申请提供了一种材料智慧专家系统，包括：多源数据管理模块，对多种类型的材料数据库中的数据进行融合，构建结构化数据库；处理模块，获取以自然语言方式输入的用户提问，用户提问包括材料需求和/或计算需求；根据材料需求在结构化数据库中检索材料属性数据，将材料属性数据转化为自然语言输出；和/或，根据计算需求生成计算流程，将计算流程的计算结果转化为自然语言输出。以此，通过多源数据管理模块打通各细分领域数据站点之间的数据屏障，构建出交叉融合的高质量数据库；以用户的研究需求为导向，通过自然语言交互的方式引导用户进行材料研究，实现智慧问答、智慧决策、自动化流程一站式材料研发，减轻材料研发人员的负担。

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/47c002c9876850c5dc97bfc2677a9f4acd320662c74970c3229692ef0c4223f6.jpg)

1. 一种材料智慧专家系统，其特征在于，所述系统包括：

多源数据管理模块，用于对多种类型的材料数据库中的数据进行融合，构建结构化数据库；

处理模块，用于获取以自然语言方式输入的用户提问，所述用户提问包括材料需求和/或计算需求；根据所述材料需求在所述结构化数据库中查询，根据所述查询的结果进行内容增强后得到材料属性信息，将所述材料属性信息转化为自然语言输出；和/或，根据所述计算需求和所述材料属性信息生成计算流程，将所述计算流程加入到任务队列中，通过计算API持续连接HPC高性能服务器执行所述计算流程，获取计算结果，将所述计算结果转化为自然语言输出。

2. 根据权利要求1所述的系统，其特征在于，所述多源数据管理模块包括：

数据采集单元，用于采集多源数据，提取多种类型的目标数据；

数据清洗单元，用于对所述多种类型的目标数据进行预处理，所述预处理包括去除重复数据、处理缺失值、处理异常值、数据格式转换；

数据标准化单元，用于对于所述预处理后的各类型目标数据制定标准，所述标准包括数据单位、数据参考系数、数据呈现形式；根据所述标准将所述预处理后的目标数据进行融合得到标准化数据；

数据再利用单元，用于根据所述标准化数据构建所述结构化数据库，基于所述结构化数据库对所述标准化数据进行索引、备份、恢复和权限管理。

3. 根据权利要求1或2所述的系统，其特征在于，所述结构化数据库存储和构建材料知识图谱，所述材料知识图谱是根据多节点类别和关系生成的；

其中所述多节点类别包括化学分子、元素、晶系、空间群、点群和拓扑类别；

所述关系包括所述节点的相关元素、所属晶系、所属空间群、所属点群和所属拓扑类型；

所述多节点类别中每类节点具有cate属性和name属性；其中，所述cate属性表示所述节点的所属类别，用于知识图谱多节点和关系的分类可视化展示；所述name属性用于唯一标识所述节点。

4. 根据权利要求1所述的系统，其特征在于，所述处理模块包括智慧问答模块，所述智慧问答模块用于获取以自然语言方式输入的所述用户提问，所述用户提问包括材料需求；

根据所述材料需求得到查询语句，根据所述查询语句检索所述结构化数据库中的材料属性数据，得到第一查询结果，所述第一查询结果包括所述材料属性的结构化信息。

5. 根据权利要求4所述的系统，其特征在于，所述智慧问答模块使用LangChain链接所述网络通用数据库，针对所述材料属性进行相似性的检索，得到第二查询结果，所述第二查询结果为与所述用户提问的材料属性具有相似结构或性质的材料信息。

6. 根据权利要求4或5所述的系统，其特征在于，所述智慧问答模块使用LangChain连接文献网站API，针对所述材料属性检索材料领域的文献，得到第三查询结果，所述第三查询结果为与所述用户提问的材料属性具有相似结构或性质的文献信息；

对所述第一查询结果、第二查询结果和/或第三查询结果进行数据增强；

通过所述大语言模型LLM根据所述增强后的数据生成自然语言后回答所述用户。

7. 根据权利要求1所述的系统，其特征在于，所述处理模块包括智能决策模块，所述智

能决策模块用于根据所述计算需求和所述材料属性信息生成计算流程，包括：

创建计算软件数据库，所述计算软件数据库用于管理各种类型的计算软件的信息；

利用LangChain框架将所述计算软件数据库中的计算软件的信息转换为向量数据；

利用LLM将所述用户提问中描述的所述计算需求和所述材料属性信息转换为向量表示，确定所述向量表示与所述向量数据的相似性的值，根据所述相似性的值确定适合用户需求的计算软件。

8. 根据权利要求7所述的系统，其特征在于，所述智能决策模块根据所述计算需求和所述材料属性信息生成计算流程，还包括：

根据所述计算需求和所述材料属性信息确定计算步骤；

将每一个计算步骤按照计算类型进行模块化得到计算模块；

将每一个计算步骤所需的输入文件、输出文件格式，硬件需求、前序步骤和后续步骤、常见错误的自动修复逻辑内置到所述计算模块中；其中所输入文件是通过第三方准备工具为每个计算步骤创建的，或通过大语言模型链接软件使用说明文档，在基本输入文件模板基础上创建的；

利用LangChain的React框架编排所述计算模块的顺序，将编排好的计算模块和所述输入文件加入到任务队列中，生成计算流程。

9. 根据权利要求8所述的系统，其特征在于，所述智能决策模块利用LangChain的React框架确定计算模块的顺序，包括：

使用顶层代理来推理选择合适的代理和工具组件，其中，所述合适的代理包括以下中的任意一个或多个组合：计算代理、文档代理、材料KG代理、材料项目代理和文献代理；所述工具组件包括包括结构获取、结构优化、掺杂原子、原子或基团吸附、结果分析、能带结构、Bader、DOS等计算模块。

10. 根据权利要求8或9所述的系统，其特征在于，所述智能决策模块通过顶层代理分析计算中间结果或错误情况，根据所述计算模块及软件说明文档分析确定调整方向，动态调整计算流程；其中，所述调整方向包括使计算结果趋向于结构优化收敛，所述调整计算流程包括自适应调整计算步骤或重新确定参数设置。

# 一种材料智慧专家系统

# 技术领域

[0001] 本申请涉及计算机技术领域，尤其涉及一种材料智慧专家系统。

# 背景技术

[0002] 材料研发在现代科技和工业领域中不可低估，是社会进步和可持续发展的关键因素之一。通过创新和发现新材料、改进和优化现有材料，材料研发在推动科技创新、改善产品性能、解决工程挑战、促进可持续发展以及提高经济增长和竞争力等方面都具有重要的作用。

[0003] 材料的研发经过了三大阶段。第一阶段主要依赖于实验室实验和测试，具有试错性质，研究人员通过多次尝试不同的材料组成、处理方法和工艺参数，以寻找最佳的材料性能。这需要进行大量的实验和测试，并且可能需要长时间才能达到理想的结果。第二阶段为理论和计算模拟阶段，强调基于理论和计算的研究方法。在这个阶段中随着计算机技术的进步，科学家们开始使用理论模型和计算机模拟方法，通过数学模型和物理原理来研究材料的行为和性质。第三阶段为数据驱动阶段，通过机器学习和人工智能技术来预测材料性能、加速材料发现和优化过程。此阶段以大数据和机器学习为基础，通过分析和挖掘大量的实验数据、计算模拟结果和文献信息，以发现材料之间的关联性、揭示隐藏的规律和趋势。[0004] 尽管三个阶段的实验、理论计算和数据驱动方法相互协作，在材料研发中取得了很大的成功，然而目前的材料研发方式仍然存在一些挑战，使得研发人员，尤其是实验为主的研发人员，在开始一项材料研究之前需要进行繁琐的手动工作。

[0005] 第一，在开始材料研发之前，研发人员需要进行文献调研，查询多个数据库和网站来获取所研究材料或相似材料的相关性质。这个过程通常需要花费大量的时间和精力，且信息来源分散，不便于整合和利用。目前已经出现一些材料研发平台和数据库整合和提供材料性质的信息，供研发人员获取所需数据。然而由于数据来源的多样性和格式的不一致性，研发人员往往还需要寻找适当的工具和技术来处理和解释数据，尤其对于一些细分专业领域的数据，理解和解释数据比较有挑战性。第二，理论模拟计算是材料研发的一个重要手段，对于理论计算和数据驱动的研发人员，手动设计和设置计算流程是一项繁琐的任务，这包括选择适当的计算方法、参数设置和模型构建等。目前一些计算平台和软件已经提供了自动或半自动化的计算流程设计工具，可以省去计算软件下载、安装编译、链接的麻烦，用户输入设计的工作流，由平台进行计算的调度和任务流转。然而，在工作流程设计方面仍然需要用户提前手动设计好，且在计算过程中计算流程无法动态调整，在智能化参数设置方面要求用户有较强的软件使用经验。这些限制会给用户，尤其是初入领域缺乏软件使用经验的用户，带来很高的学习成本和时间成本。

[0006] 以ChatGPT为例的通用大语言模型(Large Language Model，LLM)在自然语言问答方面展现出卓越的性能，能够理解并响应复杂的指令。然而，由于缺乏垂直领域的语料和训练，LLM在特定领域的性能表现较为有限。同时，训练一个专注于特定领域的大型模型对硬件系统的要求较高，这限制了垂直领域科研人员利用专业大模型推动领域研究的能力。因

此，进一步改进和优化LLM以满足特定领域需求，使其更具可扩展性，对于促进领域研究发展具有重要意义。

[0007] 另一方面，理论计算是材料研发中一个非常重要的环节，材料计算软件包含VASP、ABNIT、LAMMPS、Win2k等用于计算材料的结构和各种性质，也可以用于高通量计算流程。研究人员借助pymatgen、AFLOW等工作流工具可以实现半自动化的工作流计算。国内外也出现了一些高通量多尺度材料计算云平台，包括高通量结构建模，图形化界面的流程设计，多尺度计算，高通量并发式流程计算以及数据管理功能。平台可以省去计算软件下载、安装编译、链接的麻烦，由用户输入设计的工作流，由平台进行计算的调度和任务流转。然而，在工作流程设计方面需要用户提前手动设计好，且在计算过程中计算流程无法动态调整，在智能化参数设置方面也要求用户有较强的软件使用经验。这些限制会给用户，尤其是初入领域缺乏软件使用经验的用户，带来较高的学习成本和时间成本。

# 发明内容

[0008] 为了降低使用门槛并进一步提高智能化程度，第一方面本申请实施例提供了一种材料智慧专家系统，系统包括：多源数据管理模块，用于对多种类型的材料数据库中的数据进行融合，构建结构化数据库；处理模块，用于获取以自然语言方式输入的用户提问，用户提问包括材料需求和/或计算需求；根据材料需求在结构化数据库中查询，根据查询的结果进行内容增强后得到材料属性信息，将材料属性信息转化为自然语言输出；和/或，根据计算需求和材料属性信息生成计算流程，将计算流程加入到任务队列中，通过计算API持续连接HPC高性能服务器执行计算流程，获取计算结果，将计算结果转化为自然语言输出。

[0009] 在一些可以实现的实施方式中，多源数据管理模块包括：数据采集单元，用于采集多源数据，提取多种类型的目标数据；数据清洗单元，用于对多种类型的目标数据进行预处理，预处理包括去除重复数据、处理缺失值、处理异常值、数据格式转换；数据标准化单元，用于对预处理后的各类型目标数据制定标准，标准包括数据单位、数据参考系数、数据呈现形式；根据标准将预处理后的目标数据进行融合得到标准化数据；数据再利用单元，用于根据标准化数据构建结构化数据库，基于结构化数据库对标准化数据进行索引、备份、恢复和权限管理。

[0010] 在一些可以实现的实施方式中，结构化数据库存储和构建材料知识图谱，材料知识图谱是根据多节点类别和关系生成的；其中多节点类别包括化学分子、元素、晶系、空间群、点群和拓扑类别；关系包括节点的相关元素、所属晶系、所属空间群、所属点群和所属拓扑类型；多节点类别中每类节点具有cate属性和name属性；其中，cate属性表示节点的所属类别，用于知识图谱多节点和关系的分类可视化展示；name属性用于唯一标识节点。

[0011] 在一些可以实现的实施方式中，处理模块包括智慧问答模块，智慧问答模块用于获取以自然语言方式输入的用户提问，用户提问包括材料需求；根据材料需求得到查询语句，根据查询语句检索结构化数据库中的材料属性数据，得到第一查询结果，第一查询结果包括材料属性的结构化信息。

[0012] 在一些可以实现的实施方式中，智慧问答模块使用LangChain链接网络通用数据库，针对材料属性进行相似性的检索，得到第二查询结果，第二查询结果为与用户提问的材料属性具有相似结构或性质的材料信息。

[0013] 在一些可以实现的实施方式中，智慧问答模块使用LangChain连接文献网站API，针对材料属性检索材料领域的文献，得到第三查询结果，第三查询结果为与用户提问的材料属性具有相似结构或性质的文献信息；对第一查询结果、第二查询结果和/或第三查询结果进行数据增强；通过大语言模型LLM根据增强后的数据生成自然语言后回答用户。

[0014] 在一些可以实现的实施方式中，处理模块包括智能决策模块，智能决策模块用于根据计算需求和材料属性信息生成计算流程，包括：创建计算软件数据库，计算软件数据库用于管理各种类型的计算软件的信息；利用LangChain框架将计算软件数据库中的计算软件的信息转换为向量数据；利用LLM将用户提问中描述的计算需求和材料属性信息转换为向量表示，确定向量表示与向量数据的相似性的值，根据相似性的值确定适合用户需求的计算软件。

[0015] 在一些可以实现的实施方式中，智能决策模块根据计算需求和材料属性信息生成计算流程，还包括：根据计算需求和材料属性信息确定计算步骤；将每一个计算步骤按照计算类型进行模块化得到计算模块；将每一个计算步骤所需的输入文件、输出文件格式，硬件需求、前序步骤和后续步骤、常见错误的自动修复逻辑内置到计算模块中；其中所输入文件是通过第三方准备工具为每个计算步骤创建的，或通过大语言模型链接软件使用说明文档，在基本输入文件模板基础上创建的；利用LangChain的React框架编排计算模块的顺序，将编排好的计算模块和输入文件加入到任务队列中，生成计算流程。

[0016] 在一些可以实现的实施方式中，智能决策模块利用LangChain的React框架确定计算模块的顺序，包括：使用顶层代理来推理选择合适的代理和工具组件，其中，合适的代理包括以下中的任意一个或多个组合：计算代理、文档代理、材料KG代理、材料项目代理和文献代理；工具组件包括包括结构获取、结构优化、掺杂原子、原子或基团吸附、结果分析、能带结构、Bader、DOS等计算模块。

[0017] 在一些可以实现的实施方式中，智能决策模块通过LLM顶层代理分析计算中间结果或错误情况，根据计算模块及软件说明文档分析确定调整方向，动态调整计算流程；其中，调整方向包括使计算结果趋向于结构优化收敛，调整计算流程包括自适应调整计算步骤或重新确定参数设置。

[0018] 本申请实施例提供了一种材料智慧专家系统以用户的研究需求为导向，通过自然语言交互的方式引导用户进行材料研究，结合大语言模型实现智慧问答、智慧决策、自动化流程及容错处理，实现一站式材料研发，减轻材料研发人员的负担。

[0019] 在相关研究调研方面，系统可以通过自然语言交互进行多物性联合检索，帮助用户获取相关的文献和数据，支持专家式问答，基于其对文献或数据的理解和分析，给出准确的回答和解决方案。

[0020] 在材料计算方面，用户只需用自然语言简单地描述计算需求和材料系统的属性，系统就能自动生成适当的计算流程，并完成自动调度计算、计算任务调整、结果分析和纠错处理等，从而使材料研究变得更加灵活和智能化。

# 附图说明

[0021] 为了更清楚地说明本说明书披露的多个实施例的技术方案，下面将对实施例描述中所需要使用的附图作简单地介绍，显而易见地，下面描述中的附图仅仅是本说明书披露

的多个实施例，对于本领域普通技术人员来讲，在不付出创造性劳动的前提下，还可以根据这些附图获得其它的附图。

[0022] 下面对实施例或现有技术描述中所需使用的附图作简单地介绍。[0023] 图1是本申请实施例提供的一种材料智慧专家系统的系统架构图；[0024] 图2为本申请实施例提供的多源数据管理模块架构图；[0025] 图3为本申请实施例提供的智慧问答模块工作流程图；[0026] 图4为本申请实施例提供的智慧问答模块中将查询结果转化为自然语言示意图；[0027] 图5为本申请实施例提供的智能决策模块架构图；[0028] 图6为本申请实施例1提供的材料智慧专家系统应用流程图。

# 具体实施方式

[0029] 为了使本申请实施例的目的、技术方案和优点更加清楚，下面将结合附图，对本申请实施例中的技术方案进行描述。

[0030] 在本申请实施例的描述中，“示例性的”、“例如”或者“举例来说”等词用于表示作例子、例证或说明。本申请实施例中被描述为“示例性的”、“例如”或者“举例来说”的任何实施例或设计方案不应被解释为比其它实施例或设计方案更优选或更具优势。确切而言，使用“示例性的”、“例如”或者“举例来说”等词旨在以具体方式呈现相关概念。

[0031] 在本申请实施例的描述中，术语“和/或”，仅仅是一种描述关联对象的关联关系，表示可以存在三种关系，例如，A和/或B，可以表示：单独存在A，单独存在B，同时存在A和B这三种情况。另外，除非另有说明，术语“多个”的含义是指两个或两个以上。例如，多个系统是指两个或两个以上的系统，多个终端是指两个或两个以上的终端。

[0032] 此外，术语“第一”、“第二”仅用于描述目的，而不能理解为指示或暗示相对重要性或者隐含指明所指示的技术特征。由此，限定有“第一”、“第二”的特征可以明示或者隐含地包括一个或者更多个该特征。术语“包括”、“包含”、“具有”及它们的变形都意味着“包括但不限于”，除非是以其他方式另外特别强调。

[0033] 在本申请实施例的描述中，涉及到“一些实施例”，其描述了所有可能实施例的子集，但是可以理解，“一些实施例”可以是所有可能实施例的相同子集或不同子集，并且可以在不冲突的情况下相互结合。

[0034] 在本申请实施例的描述中，所涉及的术语“第一\第二\第三等”或模块A、模块B、模块C等，仅用于区别类似的对策，不代表针对对象的特定排序，可以理解地，在允许的情况下可以互换特定的顺序或先后次序，以使这里描述的本申请实施例能够以除了在这里图示或描述的以外的顺序实施。

[0035] 在本申请实施例的描述中，所涉及的表示步骤的标号，如S110、S120……等，并不表示一定会按此步骤执行，在允许的情况下可以互换前后步骤的顺序，或同时执行。

[0036] 本申请实施例所涉及的相关术语：

[0037] LangChain是一个基于大语言模型LLM的应用开发框架，它主要通过两种方式规范和简化了使用LLM的方式：

[0038] 集成，集成外部数据（如文件、其他应用、API数据等）到LLM中；[0039] 代理Agent，允许LLM通过决策与特定的环境交互，并由LLM协助决定下一步的操

作。

[0040] Agent就像一个多功能的接口，它能够接触并使用一套工具。根据用户的输入，Agent会决定调用哪些工具。它不仅可以同时使用多种工具，而且可以将一个工具的输出数据作为另一个工具的输入数据。

[0041] ReAct框架是根据“行动”和“推理”之间的协同作用，使得模型能够学习新任务，并做出决策或推理。

[0042] 晶体学信息文件(crystallographic information file, CIF)是以“.cif”结尾的计算机文件，它包含了每个晶体的详细信息，如晶胞参数、原子坐标、文献资料等。它是进行晶体结构描述，解析，传播和表达时最常使用的文件格式，广泛应用于晶体结构绘图，XRD精修以及材料理论计算等各个方面，在材料科学中具有着重要的作用。各种晶体/材料/计算处理软件，如Vesta, Diamond, GSAS, Mercury, Rasmal, Materials Studio，都以晶体信息cif文件作为输入文件或者输出结果。

[0043] 除非另有定义，本文所使用的所有的技术和科学术语与属于本申请的技术领域的技术人员通常理解的含义相同。本文中所使用的术语只是为了描述本申请实施例的目的，不是旨在限制本申请。

[0044] 材料研发可能会涉及材料生长、表征及应用等相关的各类数据，这些数据来源广泛，如材料细分领域的专业特色数据库，材料领域大型通用数据库、海量的文献数据等。材料领域大型通用数据库结构较为统一，而材料细分领域的专业特色数据库往往数据来源广泛，包括通过实验测试或理论计算等手段产生的大量历史数据，这些数据格式各异、标准不一，部署较为分散，存在于多种数据库中，如关系性、非关系性数据并存，难以为多种数据分析和应用提供支撑。

[0045] 本申请实施例提供了一种材料智慧专家系统，能够结合大语言模型实现结构化与非结构化多源数据的管理融合与智能分析、自动化设计材料计算工作流程、进行结果分析和容错处理等功能。

[0046] 图1是本申请实施例提供的一种材料智慧专家系统的系统架构图。如图1所示，为了实现多源数据的融合统一，本申请实施例提供的材料智慧专家系统包括多源数据管理模块11，用于对多种类型的材料数据库中的数据进行融合，构建结构化数据库；和处理模块12，用于获取以自然语言方式输入的用户提问，用户提问包括材料需求和/或计算需求；根据材料需求在结构化数据库中查询，根据查询结果进行内容增强、融合后得到材料属性信息，将材料属性信息转化为自然语言输出；和/或，根据计算需求生成计算流程，将计算流程的计算结果转化为自然语言输出。

[0047] 图2为本申请实施例提供的材料智慧专家系统的多源数据管理模块架构图。如图2所示，多源数据管理模块用于对多种类型的材料数据库中的数据进行融合，构建结构化数据库。多源数据管理模块包括数据采集单元21、数据清洗单元22、数据标准化单元23、数据再利用单元24。

[0048] 其中，数据采集单元21用于采集多源数据，提取多种类型的目标数据。

[0049] 在一些实施方式中，数据采集单元21通过多个数据站点的API接口采集多源数据，对多源数据根据细分领域提取多种类型的目标数据，如分子结构Data1、制备过程Data2、分子性质DataN等，并进行分布式存储于多种类型的材料数据库中。

[0050] 在一些实施方式中, 数据采集单元21对多个数据站点的新增的数据进行采集, 可以在低访问时段对各个站点进行增量数据的同步采集, 从而能够及时获取站点的新增数据, 最大限度降低对源站点的影响。

[0051] 数据清洗单元22对多种类型的目标数据进行预处理, 包括去除重复数据、处理缺失值、处理异常值、数据格式转换等操作, 预处理是数据融合的关键步骤。

[0052] 数据标准化单元23对预处理后的各类型的目标数据制定标准, 标准包括数据单位、数据参考系数、数据呈现形式等, 根据标准将预处理后的目标数据进行融合得到标准化数据。

[0053] 示例性地, 目标数据为晶体结构数据, 以网络通用数据库Material Project中提供了ID号的晶体结构数据为例, 数据标准化单元23可以通过转换工具如pymatgen/mp_api从预处理后的各类型的目标数据中获取晶胞参数、密度、体积等晶体结构数据, 与通用数据库中晶体结构携带的原始字段进行融合, 得到标准化数据。其中, 原始数据字段是指通用数据库中表示晶体结构的字段形式。

[0054] 融合是将晶体结构数据和晶体结构携带的原始字段进行匹配和合并来实现整合的过程, 创建更全面和综合的数据集。例如, 可以将晶胞参数和密度等晶体结构数据与原始数据字段中的晶胞参数和原子坐标等信息进行融合。

[0055] 标准化数据是指多种类型的目标数据根据指定的数据标准处理得到的数据。标准化数据使得相同物理量的量纲一致, 确保数据的一致性和可比性。

[0056] 示例性地, Material Project ID号为B10(Pb207)3分子的晶体结构的标准化数据参照表如表1所示。

[0057] 表1: 数据标准参照表(举例)

[0058]

<table><tr><td>材料属性</td><td>标准化数据
(以B10(Pb2O7)3分子为例)</td><td>单位</td></tr><tr><td>density密度</td><td>6.122</td><td>g/cm-3</td></tr><tr><td>volume体积</td><td>457.702</td><td>A3</td></tr><tr><td>cell parameters
晶胞参数</td><td>{a&#x27;: &#x27;6.84Å&#x27;, &#x27;b&#x27;: &#x27;7.01Å&#x27;, &#x27;c&#x27;: &#x27;11.14Å&#x27;,
&#x27;alpha&#x27;: &#x27;82.79°&#x27;, &#x27;beta&#x27;: &#x27;78.56°&#x27;, &#x27;gamma&#x27;: &#x27;61.02°&#x27;}</td><td>/</td></tr><tr><td>electrons_in_unit_cell</td><td>240</td><td>/</td></tr><tr><td>sites_in_unit_cell</td><td>37</td><td>/</td></tr><tr><td>nele</td><td>3</td><td>/</td></tr></table>

[0059] 数据再利用单元24用于根据标准化数据构建结构化数据库, 基于结构化数据库对标准化数据进行索引、备份、恢复、权限管理等操作, 确保数据的安全性和可管理性。

[0060] 在多源数据管理模块的架构下, 可以对材料的各类型数据库进行整合, 并在此基础上构建结构化数据库。结构化数据库用于存储和构建材料知识图谱(Material KG), Material KG是根据多节点类别和关系生成的。

[0061] 需要说明的是, 结构化数据库并不限于存储和构建材料知识图谱, 它也可以用于

其他结构类型的数据存储和构建。

[0062] 结构化数据库包括图数据库或其他结构类型的数据库。下面，以图数据库为例进行说明。

[0063] 示例性地，本申请实施例提供的材料智慧专家系统，基于多源数据管理模块，将拓扑材料数据库（http://materiae.iphy.ac.cn/）和拓扑声子数据库（http://www.phonon.synl.ac.cn/）进行融合，构建neo4j图数据库，并基于neo4j图数据库构建材料知识图谱。neo4j图数据库存储和构建拓扑材料数据，材料知识图谱包含多节点类别和关系。其中节点类别包括：化学分子Formula、元素Element、晶系Lattice、空间群Spacegroup、点群Pointgroup和拓扑类别Topoclass，关系包括：相关元素ContainsElement、所属晶系Belongs_to_Lattice、所属空间群Belongs_to_spacegroup、所属点群Belongs_to_Pointgroup和所属拓扑类型Belongs_to_Topoclass等。

[0064] 每类节点具有cate属性和name属性。其中，cate表示该节点的所属类型，用于可视化展示多节点和关系的分类；name是用于唯一标识该节点的属性字段，在不同类型的节点中不相同。

[0065] 示例性地，在Formula节点中，cate值为化学分子类别，name值为化学分子式；而在Element节点中，cate值为元素类别，name仅为元素名。

[0066] 每种关系都具有relation属性值，例如Belongs_to_Topoclass（所属拓扑类型）的relation为SOC，表示考虑自旋轨道耦合的情况，relation为NSOC表示不考虑自旋轨道耦合的情况。

[0067] Formula节点增加了拓扑声子性质相关的属性字段，例如拓扑原型proto，拓扑环上点的数量ring_pts，外尔简并点的数量weyl_pts等。

[0068] 示例性地，图数据库中节点和属性以及对应的含义如表2所示。

[0069] 表2

[0070]

<table><tr><td>节点</td><td>属性</td><td>含义</td><td>节点</td><td>属性</td><td>含义</td></tr><tr><td rowspan="18">Formula</td><td>matID</td><td>Condmat Database ID</td><td rowspan="4">Element</td><td>cate</td><td>元素类别</td></tr><tr><td>mp_id</td><td>Material Project ID</td><td>name</td><td>元素名</td></tr><tr><td>cate</td><td>化学分子类别</td><td>fullname</td><td>元素全称</td></tr><tr><td>name</td><td>化学分子式</td><td>property_of_metal</td><td>是否是金属元素</td></tr><tr><td>density</td><td>密度</td><td rowspan="4">Lattice</td><td>cate</td><td>晶系类别</td></tr><tr><td>volume</td><td>体积</td><td>name</td><td>晶系中文名称</td></tr><tr><td>cell_parame
rs</td><td>晶胞参数</td><td rowspan="2">ename</td><td rowspan="2">晶系名称</td></tr><tr><td>electrons_in_
unit_cell</td><td>每个cell中的电子数</td></tr><tr><td>sites_in_unit_
cell</td><td>每个cell中的原子数</td><td rowspan="4">Spacegroup</td><td>cate</td><td>空间群类别</td></tr><tr><td>nelet</td><td>元素数</td><td rowspan="2">name</td><td rowspan="2">空间群名</td></tr><tr><td>soc_dos_gap/
nsoc_dos_gap</td><td>考虑/不考虑自旋轨道耦合时的能隙</td></tr><tr><td>soc_fermi_do
s/nsoc_fermi_
dos_
indirect_gap/nsoc_indi
rect_gap</td><td>考虑/不考虑自旋轨道耦合时的费米能</td><td>number</td><td>空间群号</td></tr><tr><td>z</td><td>考虑/不考虑自旋轨道耦合时的非直接能隙</td><td rowspan="2">Pointgroup</td><td>cate</td><td>点群类别</td></tr><tr><td>proto</td><td>拓扑原型</td><td>name</td><td>点群名</td></tr><tr><td>lines</td><td>拓扑节线数量</td><td rowspan="4">TopoClass</td><td>cate</td><td>拓扑类别</td></tr><tr><td>ring_pts</td><td>拓扑环上点的数量</td><td>name</td><td>拓扑类别名称</td></tr><tr><td>wayl_pts</td><td>外尔简并点的数量</td><td rowspan="2">desc</td><td rowspan="2">名称解释</td></tr><tr><td>multi_deg_w
eyl_pts</td><td>多重外尔简并点的数量</td></tr></table>

[0071] 多源数据管理模块能够打通各细分领域数据站点之间的数据屏障，构建出交叉融合的高质量结构化数据库，使不同站点之间的数据能够无缝连接。

[0072] 本申请实施例提供的材料智慧专家系统包括智慧问答模块，智慧问答模块通过Langchain框架将材料知识图谱中的实体、关系和属性与大语言模型LLM的上下文理解能力相互连接，从而进行知识的交互和融合。

[0073] Langchain框架是一种用于深度融合材料知识图谱和通用大语言模型LLM的架构。Langchain框架将材料知识图谱中的结构化知识与LLM的语言生成和推理能力相结合，从而实现更深入和准确的自然语言处理。Langchain框架能够结合结构化的知识表示和语言模型的表达能力，为用户提供更加全面和准确的自然语言处理结果。Langchain框架通过将材料知识图谱与LLM进行深度融合，为专业领域的问答系统和/或智能助手等应用提供了有效的方案。

[0074] 图3为本申请实施例提供的智慧问答模块工作流程图。如图3所示，在阶段1，获得用户输入的提问，将用户提问转化为查询语句；在阶段2，根据查询语句检索材料知识图谱中的结构化信息；在阶段3，将检索到的结构化信息与用户提问结合，形成一个增强的提示，输入到LLM中进行推理，得到增强的检索结果，将增强的检索结果转化为自然语言；在阶段4，输出自然语言。

[0075] 下面分别对每一个阶段进行详细说明。

[0076] 在阶段1，根据用户提问得到查询语句。

[0077] 在一些实施方式中，可以将材料知识图谱中的结构化信息与大语言模型LLM相连接，使用LLM对用户提问进行理解和推理得到查询语句，查询语句用于查询图数据库。

[0078] 示例性地，用户提问可以是：“拓扑材料有那些常见类别”“属于拓扑绝缘体的分子中soc dos gap最大值为多少？”，“你可以推荐一些有声子性质的拓扑绝缘体吗？”“属于拓扑绝缘体的分子大多包含什么元素？”“由碱金属元素构成的材料可能是拓扑绝缘体吗？”此类问题对于引导LLM挖掘图数据库中数据的复杂关联关系有重要作用，也可用于相似性材料的推荐中，使用LLM对用户提问进行理解和推理，在“#任务”文本框内显示“生成Cypher语句来查询图数据库”。

[0079] 为了充分激发LLM的语言模型的能力，帮助自然语言转化为图数据库的查询语句并将图数据库的查询结果转化为自然语言，在一些实施方式中，可以使用基于prompt的提示学习，根据用户提问设置符合需求的prompt，以帮助大语言模型理解任务。

[0080] 示例性地，可以在“#提示”文本框内设置“根据用户提问描述的材料性质或推荐材料”。

[0081] 在一些实施方式中，通过给LLM提供少样本学习举例(FewShot)，帮助其理解用户提问，根据用户提问生成Cypher查询语句。

[0082] 在一些实施方式中，可以根据用户提问及上下文确定待查询的图数据库模式。

[0083] 在一些实施方式中，可以设置注意条款以约束返回答案的范围。如在“#注意”文本框内设置“不要在你的回答中包含解释和道歉”“不要回答任何可能要求您构建Cypher语句以外的问题”“除了生成的Cypher语句外，不要包含任何文本”等注意条款的语句，以约束返回答案的范围。

[0084] 在阶段2，对于用户提问包括材料需求，根据查询语句检索图数据库中的数据，得到第一查询结果，第一查询结果包括对应的结构化信息。

[0085] 在一些实施方式中Material KG包括拓扑材料知识图谱，根据Cypher查询语句，可以进入图数据库查询，得到对应拓扑材料的结构化信息。

[0086] 材料科学领域的专业知识包括材料相关的基本知识。拓扑材料是材料细分领域更为深入的数据，一般的材料项目(Material Project)等通用数据库不包括这些信息。

[0087] 可以理解的是，Mateiral KG里面涉及到的材料包含于Material Project，但是Mateiral KG相比于Material Project在细分领域(如拓扑材料领域)的数据维度更多，更详细，更有特色。

[0088] 在阶段3，进行增强内容查询，将增强内容的查询结果与用户提问结合，形成一个增强的提示，输入到LLM中生成自然语言。

[0089] 对于用户提问中涉及的材料数据领域的专业知识还可以通过对网络通用数据库的增强查询获得，材料领域的网络通用数据库也是材料智慧问答的重要数据来源之一。材料领域的网络通用数据库包括Materials Project，Material Project包含了大量的材料属性数据，这些材料属性数据来自于密度泛函理论(DFT)计算和实验测量等多种来源，为研究人员提供了广泛的材料信息。

[0090] 在一些实施方式中，可以使用LangChain链接网络通用数据库，进行相似性的检索，得到第二查询结果，第二查询结果为与用户提问的材料属性具有相似结构或性质的材

料信息。

[0091] 示例性地, 可以借助LangChain提供的APIChain链接材料领域的网络通用数据库如Materials Project查询获取材料的各类参数和性质; 包括结构信息、晶体学参数、能带结构、能量、弹性性质、热力学性质等数据; 通过LangChain的Agent组件将Prompts、APIOperation工具和LLMChain相连接进行增强内容查询, 获取相似结构或性质的材料。增强查询包括对网络通用数据库进行相似性检索获取第二查询结果。

[0092] 材料科学领域的专业知识还可以通过对文献网站提供的海量文献数据的增强查询获得。在一些实施方式中, 可以使用LangChain连接文献网站API, 检索材料领域的文献, 得到第三查询结果, 第三查询结果为与用户提问的材料属性具有相似结构或性质的文献信息。

[0093] 示例性地, 对于用户提问中涉及的材料数据领域的专业知识, 首先, 通过LangChain连接文献网站API, 如arXiv API, 可以获取材料领域的文献, 借助LangChain的LEDVR工作流工具对文献分批进行处理, 通过Loader文档加载器将分批处理的文献加载到内存得到材料文档, 通过Document文档转换器将材料文档进行分割和数据转换得到文本数据, 通过Embedding模型如text2vec- large- chinese模型包装器将文本数据转换为向量数据, 通过Vectorstore系统的向量数据库(如Chroma)存储向量数据。接下来, 对于用户提问, LLM大语言模型将问题嵌入为向量数据, 通过Retriever检索器访问向量数据库(如Chroma)进行相似性检索, 获取与用户提问的材料属性具有相似结构或性质的文献信息, 文献信息为非结构化信息。

[0094] 对第一查询结果、第二查询结果和/或第三查询结果进行内容增强, 由大语言模型LLM对增强的内容进行分析和润色后生成为自然语言。通过内容增强能够使LLM从材料知识图谱中获取专业领域的背景知识, 并将专业领域的背景知识应用于自然语言交互和问题回答的过程中。

[0095] 示例性地, 图4为本申请实施例提供的智慧问答模块中增强查询内容示意图。如图4所示, 当用户提问的问题文本进入系统时, 优先查询Material KG, 如果有对应的查询结果, 则以Material KG查询结果为主  $(80\%)$ , 以文献相似性检索内容为参考  $(20\%)$  进行融合, 通过LLM对融合后增强的内容进行分析和润色后生成自然语言。如果Material KG查询结果为空, 则以检索网络(Web)通用数据库的查询结果为主  $(80\%)$ , 检索文献相似性内容为参考  $(20\%)$  进行融合, 通过LLM对融合后增强的内容进行分析和润色, 生成为自然语言。如果Material KG和网络通用数据库都查询为空, 则返回更多条文献相似性检索内容(包含来源的文献), 经过LLM对多条相似性检索内容融合处理后生成自然语言。如果文献相似性检索结果达不到设定的相似性阈值, 则由LLM模型本身对问题的理解和推理进行回答, 没有额外融合其他信息。

[0096] 示例性地, 返回图2所示, 在阶段3, “#任务”文本框内设置“基于Cypher查询结果生成标准化和人类可以理解的回答”; 在“#提示”文本框内可以设置并显示“如果Cypher查询结果不为空, 则必须将其转换为自然语言描述。如果Cypher查询结果为空, 则根据问题生成答案。请以材料科学家的口吻回答问题, 并确保您的回答准确无误。”, 该提示用于设置增强查询结果的生成条件; 在“#属性含义”文本框内显示Cypher查询结果字段的详细含义; 在“#上下文”文本框内显示Material KG查询结果和用户提问的上下文; 基于上述设置, 系统实

现了基于Cypher查询结果生成标准化和人类可以理解的回答。

[0097] 在一些实施方式中, 还可以在增强的提示中设置回复文本对应的参考文献和Web数据网站的链接, 以此在输出的自然语言中给出回复文本对应的参考文献和Web数据网站的链接。

[0098] 示例性地, 在阶段3的“#注意”文本框内设置“如果Cypher查询结果中提供了matID (非mpid), 请添加前缀“http://materiae.iphy.ac.cn/materia1s”作为参考, 否则在回复中显示“http://materiae.iphy.ac.cn”作为参考。”等提示语句, 以给出回复文本对应的参考文献和Web数据网站的链接, 从而以增强回答的准确性和可信度。

[0099] 在阶段S4, 输出自然语言。

[0100] 示例性地, 对阶段1中的用户提问“拓扑材料有那些常见类别”进行如阶段2的检索和阶段3的增强检索结果转化为自然语言后, 阶段4输出的Cypher结果语句为“在拓扑材料领域, 一些常见的类别包括平凡绝缘体、一般动量半金属、拓扑绝缘体、拓扑晶体绝缘体、高对称性线半金属和高对称性点半金属。这些类代表了不同的结构和电子特性, 使它们在材料科学领域具有重要意义。如果你想探索更多关于这些拓扑材料的信息, 你可以参考http://materiae.iphy.ac.cn获取更多信息。”

[0101] 以上是本申请提供的材料智慧专家系统的中智慧问答模块工作流程的介绍。

[0102] 本申请实施例提供的材料智慧专家系统借助LangChain框架, 将材料搜索查询与计算通过自然语言方式实现, 用户只需要以自然语言的方式输入简单的需求即可完成。

[0103] 本申请实施例提供的材料智慧专家系统结合大语言模型的语义理解能力, 通过自然语言交互进行多物性联合检索, 帮助用户获取相关的文献和数据, 通过自然语言专家式问答, 基于其对文献或数据的理解和分析, 给出准确的回答和解决方案, 帮助用户发现与其研究对象相似的材料, 并提供相关的特性和性能信息, 提供更全面的材料选择和设计的支持。

[0104] 在材料智慧专家系统基于智慧问答模块已经明确了任务中需要计算的材料需求和材料属性之后, 对于用户而言, 开展计算还需要选择合适的计算工具, 设计中间的计算步骤, 生成对应的输入文件。

[0105] 本申请实施例提供的材料智慧专家系统还包括智能决策模块, 智能决策模块用于根据计算需求和材料属性, 确定计算步骤和计算工具, 自动为计算流程设置合适的参数, 生成对应的输入文件。

[0106] 智能决策模块通过LangChain框架和大语言模型LLM为用户选择合适的计算工具组件, 确定中间的计算步骤。

[0107] 在一些实施方式中, 智能决策模块为用户选择合适的计算工具组件的流程如下:

[0108] 首先, 创建一个计算软件数据库, 计算软件数据库用于管理各种类型的计算软件的信息, 这些信息包括功能特色、适用范围、扩展性、案例及BenchMark数据, 并将这些计算软件的信息保存为非结构化数据。其中, 各种类型的计算软件包括VASP、ABINIT、CP2K等。

[0109] 然后, 利用LangChain框架中的语言编码的深度向量检索(language- encoded deep vector retrieval, LEDVR)功能, 将计算软件数据库中的非结构化数据转换为向量数据。

[0110] 利用LLM的Embeddedding将用户提问中描述的计算需求和材料属性转换为向量表

示，确定这些向量表示与计算软件数据库中的向量数据的相似性的值，根据相似性的值确定适合用户需求的计算软件，通过LLM向用户推荐适合用户计算需求的计算软件。

[0111] 在一些实施方式中，智能决策模块根据计算需求和材料属性信息确定中间的计算步骤，在推荐计算流程方面，智能决策模块的流程如下：

[0112] 首先，将每一个计算步骤按照计算类型进行模块化得到计算模块；将每一个计算步骤所需的行为输入文件、输出文件格式，各行为的硬件需求（如CPU/GPU/内存）、前序步骤和后续步骤、常见错误的自动修复逻辑等内置到相应的计算模块中。

[0113] 示例性地，计算模块包括结构获取、结构优化、掺杂原子、原子或基团吸附、结果分析、能带结构、Bader、DOS等。

[0114] 其中，每个计算步骤所需的的行为输入文件是通过第三方准备工具创建的；或通过大语言模型链接软件使用说明文档，在基本输入文件模板基础上创建的。

[0115] 软件使用说明文档是软件官方提供的文档，例如VASP软件官方网站会提供UserMaual，来指导用户使用软件。软件使用说明文档提供了软件输入文件参数的使用说明，对于每个步骤中涉及到的输入文件的创建有帮助。

[0116] 基本输入文件模板是提前下载好的第三方库中存取的模板中得到的。

[0117] 前序步骤是执行当前计算模块必须依赖的其他计算模块，例如能带计算模块，需提供一个经过结构优化收敛的结构作为输入，需要将结构优化作为能带计算模块的前序步骤。

[0118] 后续步骤是当前计算模块执行后可以进行的计算或分析，例如结构优化模块完成后，可以进行能带结构计算，则将能带计算作为结构优化模块的后续步骤。

[0119] 然后，根据计算模块所描述的前序步骤和后续步骤来编排顺序，将编排好的计算步骤和行为输入文件加入到任务队列中，生成计算流程。

[0120] 在一些实施方式中，利用大语言模型LLM的推理引擎代理选择调度合适计算模块形成计算流程。

[0121] 智能决策模块20可以通过LangChain的ReAct框架实现计算流程设计和调整，将下一步计算流程的推理和行动融合在一起。ReAct框架是大语言模型LLM能够作为“智能代理”自主、连续、交错地生成推理轨迹和任务特定操作的理论基础。其中的观察(observation)和思考(thought)，是“推理”(reasoning)过程，“推理”指导着“行动”(action)。

[0122] 示例性地，图5为本申请实施例提供的智能决策模块的ReAct框架图。如图5所示，在智能决策模块20中，可以将每个具体的计算模块设置为特定的工具组件并包装为计算代理(computer agent)42，将每个计算步骤所需的行为输入文件设置为文档代理43；设置顶层代理Top React Agent41；在接收到用户提问后，使用Top React Agent41来推理选择合适的Agent和工具组件，并通过计算API持续连接HPC高性能服务器获取计算结果。

[0123] 其中合适的Agent包括但不限于以下代理中的任意一个或多个组合：计算代理、文档代理、材料KG代理、材料项目代理和文献代理。

[0124] 工具组件包括结构获取、结构优化、掺杂原子、原子或基团吸附、结果分析、能带结构、Bader、DOS等计算模块。

[0125] 在一些实施方式中，可以使用Top React Agent41来推理选择合适的Agent和工具组件，包括根据文档代理43提供的输入文件，Top React Agent41确定需要调用的计算模

块，通过计算代理(computer agent)42编排计算模块的执行顺序，将一个计算模块的输出数据作为另一个计算模块的输入数据，形成计算流程。

[0126] 智能决策模块20具备自适应能力，能够根据中间的计算结果或错误情况动态调整计算流程。在某个计算步骤出现错误或者计算结果不符合预期的情况下，智能决策模块自动调整计算流程和步骤，或重新确定计算策略或参数设置。

[0127] 在一些实施方式中，智能决策模块通过LLM顶层代理分析计算中间结果或错误情况，根据计算模块及软件说明文档分析确定调整方向，动态调整计算流程；其中，调整方向包括使计算结果趋向于结构优化收敛，调整计算流程包括自适应调整计算步骤或重新确定参数设置。

[0128] 在一些实施方式中，可以基于LangChain的基础回调处理器为每个计算工具组件开发自定义回调处理器，在计算工具组件计算结束或错误时触发回调处理，以实现对于计算流程的自适应调整。

[0129] 在计算结束的情况下，可以触发回调处理器提取计算结果，并交由Top React Agent41激活下一步骤计算工具。

[0130] 在计算出错的情况下，触发回调处理器处理计算错误，经由大语言模型LLM提取错误语句并与“error与solution”数据库中错误类型进行匹配，找到错误解决方法，交由Top React Agent41激活其他计算工具或调整输入文件，重新应用新的计算工具组件执行计算。

[0131] 本申请实施例提供材料智慧专家系统，通过集成实现大语言模型与LangChain框架的结合可以为用户提供全面的材料研究支持。该系统涵盖了文献分析、数据分析、材料相似性分析与检索、材料结构获取与修饰、材料计算流程设计与调度计算、结果分析以及容错处理等功能，特别适用于实验为主的材料科研人员，为他们进行材料相关研究提供了方便快捷的工具，降低了使用多来源文档或工具软件进行研究的门槛。

[0132] 实施例1

[0133] 图6为本申请实施例1提供的材料智慧专家系统应用流程图。如图6所示，用户提问“Bi₂Se₃是拓扑绝缘体材料么？”系统通过顶层代理(Top- React agent)51可以首先通过智慧问答模块进行数据查询的增强，通过材料知识图谱代理(material KG agent)54查询本地材料知识图谱，获取查询结果。若没有返回查询结果，则通过材料项目代理(material project agent)53查询网络通用数据库获取材料数据，如晶体学参数、能带结构、能量、弹性性质、热力学性质等，材料知识图谱代理54和/或材料项目代理53可以融合文献代理52查询文献的结果，反馈用户更为丰富准确的CIF信息。如通过查询后，用户得不到Bi₂Se₃是否为拓扑绝缘体材料的直接描述，可以选择智能决策模块，调用计算代理55根据CIF信息来判断Bi₂Se₃是否为拓扑绝缘体，将计算结果解析为非结构化数据，入库到材料知识图谱数据库中供其他用户查询。

[0134] 本申请实施例1提供的材料智慧专家系统通过集成多个数据库和材料相关网站的数据，能够自动获取和整合多类材料相关数据，提供用户所需的全面信息，避免了用户需要查询多个数据库和网站的麻烦。

[0135] 实施例2

[0136] 本申请实施例2提供的以VASP研究“在RuO₂晶体中掺杂Fe原子并吸附OH基团的吸附能分析”为例，说明材料智慧专家系统中的智能决策模块进行动态工作流规划和自适应

调整的策略，涉及的步骤包括：

[0137] S51，从Material Project获取RuO的晶体结构CIF文件，通过pymatgen进行切面填充真空。

[0138] S52，通过vaspkit生成结构优化计算所需的INCAR、POSCAR、KPOINTS和POTCAR输入文件。

[0139] S53，优化之后的结构随机掺杂20%的Fe原子，进行掺杂结构优化，之后通过pymatgen识别晶体中的空位并进行OH基团吸附，进行吸附结构的优化。

[0140] S54，进行结构修正值计算，统计吸附物系的能量和修正值，通过公式计算出吸附能G。

[0141] 从上面流程S51- S54可以看出，计算模块包括以下几种：结构获取模块、结构编辑(掺杂&吸附)模块、结构优化模块和结果分析模块，其中结构优化模块包括输入文件创建。

[0142] 在本申请实施例2提供的材料智慧专家系统中，可以将涉及到的上述几种计算模块自定义为VASP计算工具组件，在计算工具组件内包括流程中用到的各种功能的工具，如结构获取工具、结构编辑(掺杂&吸附)工具、结构优化(包括输入文件创建)工具和结果分析工具，每个工具由名称(Name)和描述(Description)组成。

[0143] 示例性地，一个结构优化工具的样例如下：

[0144]

名称：结构优化

描述：该工具组件对给定的晶体结构进行优化，以获得最低能量的稳定结构。它利用VASPkit准备输入文件，并利用VASP软件进行能量最小化计算，在迭代优化过程中采用指定的收敛标准和优化算法。该工具组件的输入是表面刻面和真空填充晶体结构、掺杂晶体结构或吸附晶体结构，包括结构文件（POSCAR）和计算参数文件（INCAR、KPOINTS、POTCAR）。它通过调用VASP计算引擎来执行结构优化计算，并生成优化的结构。在优化过程中，原子位置和单元参数会更新，直到满足收敛标准或达到最大迭代次数。

此工具组件的主要功能包括：

- 加载初始晶体结构和计算参数文件 
- 设置优化算法和收敛标准- 利用VASPKit生成用于VASP计算的输入文件- 调用VASP计算引擎执行结构优化计算- 在优化过程中监控能量和力，检查收敛并满足预定义的收敛标准- 根据收敛标准生成优化的结构文件

[0145] 其中每个工具组成特定的计算代理(computer agent)，并采用Top React Agent完成不同工具的调用。

[0146] 为材料专家系统提供一个增强的Prompt模板样例如下所示。

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/af49b4fbec2e54a9e473e037d37aa527aec7c7f8eb9b41c39cf4b4f8fca84969.jpg)

[0148] 在一些实施方式中，可以为每个计算模块内置callback回调事件，用以在计算模块调用结束on_tool_end时处理结果的输出，或处理工具调用异常on_tool_error执行过程中的错误。

[0149] 示例性地，在结构获取(Structural Optimization)工具定义的callback回调事件的伪代码样例如下所示：

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/e9ad4e623e1fdd61918877c9dd4555eabe2b8f293da41658c309b992256a9e67.jpg)

[0151] 通过以上设计，借助LangChain的Top React Agent调用VASP计算工具组件可以完成RuO晶体中掺杂Fe原子并吸附OH基团的催化性质分析，为用户提出的计算需求“在RuO晶体中掺杂Fe原子并吸附OH基团的吸附能分析”自动生成一个可能的流程样例如下所示。

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/d1ab0c57135f3605c06ffb0a65f44b639f7c1d0cdaad494502923a21cd373857.jpg)

[0153] 采用本申请实施例2提供的材料智慧专家系统中，智能决策模块将大大降低用户的学习和操作门槛，使材料研究更加灵活和智能化，用户只需简单描述自己的需求，智能决策模块将自动选择合适的计算软件、设计计算流程，并自动调度计算任务。

[0154] 在计算过程中，智能决策模块根据中间结果和错误情况进行自适应调整，提供高效而准确的计算结果。

[0155] 本申请实施例提供的系统能够根据用户的提出的简单需求，智能决策选择合适的计算软件、设计计算流程，并自动调度计算任务。在计算过程中，根据中间结果和错误情况进行自适应调整，提供高效而准确的计算结果，减少了用户手动设计和设置计算流程的工作量。

[0156] 本申请实施例提供的材料智慧专家系统通过集成大语言模型实现了自动化的文献调研、多源数据整合与智能分析、材料相似性分析与检索、材料工作流计算以及结果分析

和容错处理等功能,可降低材料研发人员的使用门槛,提高材料研究的效率和便捷性。[0157] 可以理解的是,本申请的实施例中的处理器可以是中央处理单元 (central processing unit, CPU),还可以是其他通用处理器、数字信号处理器 (digital signal processor, DSP)、专用集成电路 (application specific integrated circuit, ASIC)、现场可编程门阵列 (field programmable gate array, FPGA) 或者其他可编程逻辑器件、晶体管逻辑器件,硬件部件或者其任意组合。通用处理器可以是微处理器,也可以是任何常规的处理器。

[0158] 本申请的实施例中的方法步骤可以通过硬件的方式来实现,也可以由处理器执行软件指令的方式来实现。软件指令可以由相应的软件模块组成,软件模块可以被存放于随机存取存储器 (random access memory, RAM)、闪存、只读存储器 (read- only memory, ROM)、可编程只读存储器 (programmable rom, PROM)、可擦除可编程只读存储器 (erasable PROM, EPROM)、电可擦除可编程只读存储器 (electrically EPROM, EEPROM)、寄存器、硬盘、移动硬盘、CD- ROM或者本领域熟知的任何其它形式的存储介质中。一种示例性的存储介质耦合至处理器,从而使处理器能够从该存储介质读取信息,且可向该存储介质写入信息。当然,存储介质也可以是处理器的组成部分。处理器和存储介质可以位于ASIC中。

[0159] 在上述实施例中,可以全部或部分地通过软件、硬件、固件或者其任意组合来实现。当使用软件实现时,可以全部或部分地以计算机程序产品的形式实现。所述计算机程序产品包括一个或多个计算机指令。在计算机上加载和执行所述计算机程序指令时,全部或部分地产生按照本申请实施例所述的流程或功能。所述计算机可以是通用计算机、专用计算机、计算机网络、或者其他可编程装置。所述计算机指令可以存储在计算机可读存储介质中,或者通过所述计算机可读存储介质进行传输。所述计算机指令可以从一个网站站点、计算机、服务器或数据中心通过有线 (例如同轴电缆、光纤、数字用户线 (DSL)) 或无线 (例如红外、无线、微波等) 方式向另一个网站站点、计算机、服务器或数据中心进行传输。所述计算机可读存储介质可以是计算机能够存取的任何可用介质或者是包含一个或多个可用介质集成的服务器、数据中心等数据存储设备。所述可用介质可以是磁性介质, (例如,软盘、硬盘、磁带)、光介质 (例如, DVD)、或者半导体介质 (例如固态硬盘 (solid state disk, SSD)) 等。

[0160] 可以理解的是,在本申请的实施例中涉及的各种数字编号仅为描述方便进行的区分,并不用来限制本申请的实施例的范围。

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/ee609ef01c6a7eff8560a636516d641cfa15ebe03dd1d5d8c87ec0b6e0c0328c.jpg)  
图1

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/06233e2f14a56312c8969e0e1018cc20358434ee76aea9332c5b2b53ef62b348.jpg)  
图2

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/de345fdda354c6b2653c71a113a0670f6a2b64b564b9e77cbfb579658d791f2c.jpg)  
图3

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/dac52f74a381f19a56ff06e750a918b4c4f065c87cb73b4691b760b03c26c420.jpg)  
图4

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/025413a916de1037540650086b89640d1a6e9b298cade569abd19e0df28a0ad4.jpg)  
图5

![](https://cdn-mineru.openxlab.org.cn/result/2025-08-26/b8eb5a18-87a0-4f5e-8ff1-fd5d170d2a7b/63d75a1852959e744705210fa7c95ea564f8acd34c9e3c8af1cd3978a49bb08c.jpg)  
图6