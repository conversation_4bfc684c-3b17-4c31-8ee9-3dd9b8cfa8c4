# MCMD（磁热材料数据库）
[![English](https://img.shields.io/badge/Language-English-blue)](README.en.md)

## 项目介绍
MCMD（Magnetocaloric Materials Database）是一个专为磁热材料科学研究设计的数据管理系统，致力于提供磁热材料的晶体结构数据存储、查询、可视化和分析功能。本系统主要面向材料科学研究人员和学生，帮助他们高效管理、分析和利用磁热材料数据，促进磁热材料研究和磁制冷技术的发展。

## 系统架构
系统采用前后端分离的微服务架构：
- **前端**：基于Vue 3框架开发，使用Vuetify 3组件库构建现代化响应式用户界面
- **后端**：使用Java 21 + Spring Boot 3.5开发REST API，采用多模块Maven项目结构
- **数据库**：使用MongoDB存储晶体结构和磁热材料特性数据，支持复杂查询和索引优化
- **存储**：集成腾讯云对象存储(COS)保存CIF晶体文件和XRD数据文件
- **安全**：集成JWT认证、Argon2密码加密、CORS安全配置和访问频率限制

## 核心功能

### 数据管理与存储
- **材料数据管理**：支持CIF格式晶体文件的批量上传、存储和管理
- **文件云存储**：集成腾讯云COS，实现晶体文件的安全云端存储和访问
- **数据导入导出**：支持材料数据的批量导入和多格式导出功能

### 可视化与分析
- **晶体结构可视化**：使用ChemDoodleWeb和Three.js提供晶体结构的3D交互式展示
- **XRD谱图生成**：自动计算并显示X射线衍射谱图，支持多种波长（CuKα、CuKα1、CuKα2）
- **数据图表展示**：使用ECharts和Chart.js提供丰富的数据可视化图表

### 搜索与查询
- **高级搜索功能**：支持按化学式、空间群、晶胞参数、磁热性能等多维度精确搜索
- **智能搜索**：集成LangChain4J，提供AI驱动的智能材料查询和分析
- **元素周期表查询**：集成交互式元素周期表，支持元素选择和材料筛选

### 安全与权限
- **用户认证系统**：基于JWT的安全认证，支持用户注册、登录和权限管理
- **角色权限控制**：区分普通用户和管理员权限，实现数据访问控制
- **安全防护**：集成访问频率限制、CORS安全配置和密码加密保护

## 技术特点

### 架构设计
- **微服务架构**：后端采用多模块Maven项目结构，包括API、Service、DAO、POJO和Common模块
- **现代化技术栈**：前端使用Vue 3 + Vuetify 3，后端使用Spring Boot 3.5 + Java 21
- **响应式设计**：支持多种设备和屏幕尺寸，提供优秀的用户体验

### 数据处理
- **高性能数据库**：MongoDB数据库配置索引优化，支持复杂查询和大数据量处理
- **智能分析**：集成LangChain4J框架，提供AI驱动的材料分析和查询功能
- **多格式支持**：支持CIF文件解析、XRD数据处理和多种数据导出格式

### 可视化技术
- **3D晶体结构**：使用ChemDoodleWeb和Three.js实现交互式3D晶体结构展示
- **图表可视化**：集成ECharts和Chart.js，提供丰富的数据图表和统计分析
- **响应式界面**：基于Vuetify Material Design，适配各种设备屏幕

### 安全与性能
- **企业级安全**：JWT认证、Argon2密码加密、CORS配置和访问频率限制
- **云存储集成**：腾讯云COS对象存储，确保文件安全和高可用性
- **性能优化**：前端代码分割、后端缓存策略和数据库索引优化

## 安装与部署

### 前端部署
```bash
cd MCMD_frontend
npm install
npm run build
```

### 后端部署
```bash
cd MCMD_backend
mvn clean package
java -jar api/target/mcmd_backend.jar
```

### Docker部署
前端和后端均提供Dockerfile，可快速构建Docker镜像：
```bash
# 构建前端镜像
cd MCMD_frontend
docker build -t mcmd-frontend .

# 构建后端镜像
cd MCMD_backend
docker build -t mcmd-backend .
```

## 技术栈详情

### 前端技术
- **框架**: Vue 3.5.16 + Vue Router 4.2.4
- **UI组件**: Vuetify 3.3.16 (Material Design)
- **图表库**: ECharts 5.6.0 + Chart.js 4.4.0 + Vue-ChartJS 5.2.0
- **3D可视化**: Three.js 0.158.0 + ChemDoodleWeb
- **工具库**: Axios 1.6.2, JSZip 3.10.1, Marked 15.0.12
- **开发工具**: Vue CLI 5.0.8, Sass 1.69.0, ESLint

### 后端技术
- **核心框架**: Spring Boot 3.5.0 + Java 21
- **数据库**: Spring Data MongoDB 3.5.0
- **安全认证**: JWT (JJWT 0.11.5) + Argon2 2.11
- **云存储**: 腾讯云COS API 5.6.227
- **AI集成**: LangChain4J 1.0.1 (OpenAI Spring Boot Starter)
- **构建工具**: Maven 多模块项目结构

### 部署与运维
- **容器化**: Docker + Dockerfile
- **数据库**: MongoDB 索引优化脚本
- **安全**: CORS配置、访问频率限制、安全检查脚本

## 开发团队
- 中国科学院大学材料科学与光电技术学院闫清波教授课题组

## 开源许可
本项目采用MIT许可证