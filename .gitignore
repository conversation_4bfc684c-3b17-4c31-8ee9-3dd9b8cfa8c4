# macOS system files
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Java related
*.class
*.log
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*
replay_pid*
*.hprof

# Maven related
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Node.js related
node_modules/
npm-debug.log
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm/
.yarn/
.pnp.*
.env
.env.development.local
.env.test.local
.env.production.local
.env.local
coverage/
dist/

# IDEs and editors
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.settings/
*.sublime-workspace
.factorypath
*.iml
*.iws
*.ipr
modules.xml
.idea/workspace.xml
.idea/misc.xml
.idea/compiler.xml
.idea/libraries/
.idea/artifacts/
.idea/dataSources/
.idea/dataSources.local.xml
.idea_modules/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Tests
/coverage
/test-results/
/playwright-report/
/playwright/.cache/

# MongoDB data
*.lock
*.bson
*.wt
*.0*
mongod.lock
WiredTiger
WiredTiger.lock
WiredTiger.wt
WiredTiger.turtle

# Application specific
application-local.yml
application-dev.yml
application-prod.yml
secret.properties
/uploads/
.env.local

# COS temporary files
.cos.temp/
.cos.cache/

# Spring Boot
*.pid
.springBeans
.spring.lock

# Temporary files
tmp/
temp/
