package com.tcmp2.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-03-02 19:53
 * @description :Web配置类
 * 用于注册过滤器和其他Web相关配置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    // URL格式验证正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+(:[0-9]+)?$"
    );

    @Autowired
    private RateLimitInterceptor rateLimitInterceptor;

    @Value("${cors.allowed-origins:http://localhost:8080}")
    private String allowedOriginsString;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册速率限制拦截器
        registry.addInterceptor(rateLimitInterceptor)
                .addPathPatterns("/**"); // 应用到所有路径
    }

    @Bean
    public FilterRegistrationBean<JwtAuthFilter> jwtFilter(JwtAuthFilter filter) {
        FilterRegistrationBean<JwtAuthFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/materials/*", "/search/*", "/update_material/*", "/add_material", "/delete_material/*", "/delete_all_materials", "/data-count", "/test_mongodb_connection", "/auth/*", "/user/*", "/export/*");  // 添加需要保护的URL模式
        registrationBean.setOrder(1);  // 设置过滤器顺序
        return registrationBean;
    }
    
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true); // 允许携带cookie

        // 从环境变量或配置文件中获取允许的源，默认为localhost:8080
        List<String> allowedOrigins = Arrays.asList(allowedOriginsString.split(","));

        // 验证和清理允许的源
        for (String origin : allowedOrigins) {
            String trimmedOrigin = origin.trim();
            if (!trimmedOrigin.isEmpty()) {
                // 验证源格式是否合法
                if (isValidOrigin(trimmedOrigin)) {
                    config.addAllowedOriginPattern(trimmedOrigin);
                } else {
                    logger.warn("忽略无效的CORS源配置: {}", trimmedOrigin);
                }
            }
        }

        // 如果没有配置有效的源，添加默认的开发环境源
        if (config.getAllowedOriginPatterns() == null || config.getAllowedOriginPatterns().isEmpty()) {
            logger.warn("未配置有效的CORS源，使用默认开发环境配置");
            config.addAllowedOriginPattern("http://localhost:8080");
            config.addAllowedOriginPattern("http://127.0.0.1:8080");
        }

        // 严格限制允许的HTTP方法
        config.addAllowedMethod("GET");
        config.addAllowedMethod("POST");
        config.addAllowedMethod("PUT");
        config.addAllowedMethod("DELETE");
        config.addAllowedMethod("OPTIONS");

        // 严格限制允许的请求头
        config.addAllowedHeader("Authorization");
        config.addAllowedHeader("Content-Type");
        config.addAllowedHeader("Accept");
        config.addAllowedHeader("Cache-Control");
        config.addAllowedHeader("X-Requested-With");
        config.addAllowedHeader("Last-Event-ID");  // EventSource需要

        // 限制允许前端访问的响应头
        config.addExposedHeader("Content-Type");
        config.addExposedHeader("Cache-Control");
        config.addExposedHeader("Connection");
        // 注意：Set-Cookie头在生产环境中可能不需要暴露

        // 设置预检请求的缓存时间（秒）
        config.setMaxAge(3600L);

        source.registerCorsConfiguration("/**", config);
        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
        bean.setOrder(0);  // 确保CORS过滤器在JWT过滤器之前执行
        return bean;
    }

    /**
     * 验证CORS源是否为有效的URL格式
     * @param origin 要验证的源
     * @return 是否有效
     */
    private boolean isValidOrigin(String origin) {
        if (origin == null || origin.trim().isEmpty()) {
            return false;
        }

        // 检查是否匹配基本的URL格式
        if (!URL_PATTERN.matcher(origin).matches()) {
            return false;
        }

        // 额外的安全检查：不允许通配符
        if (origin.contains("*")) {
            return false;
        }

        // 不允许不安全的协议（除了开发环境的http）
        if (origin.startsWith("http://") && !isLocalDevelopmentOrigin(origin)) {
            logger.warn("生产环境不建议使用HTTP协议的源: {}", origin);
            // 在开发环境允许，生产环境建议使用HTTPS
        }

        return true;
    }

    /**
     * 检查是否为本地开发环境的源
     * @param origin 源地址
     * @return 是否为本地开发环境
     */
    private boolean isLocalDevelopmentOrigin(String origin) {
        return origin.contains("localhost") ||
               origin.contains("127.0.0.1") ||
               origin.contains("0.0.0.0");
    }
}