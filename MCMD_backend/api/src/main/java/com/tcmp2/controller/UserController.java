package com.tcmp2.controller;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.UserService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 10:00
 * @description : 处理用户注册、管理相关的API请求
 */
@RestController
@RequestMapping("/user")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Resource
    private UserService userService;
    
    @Resource
    private JwtUtil jwtUtil;
    

    
    /**
     * 管理员创建用户
     */
    @PostMapping("/create")
    public ResponseEntity<?> createUser(@RequestBody Map<String, String> createRequest, HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }

        String username = createRequest.get("username");
        String password = createRequest.get("password");
        String email = createRequest.get("email");
        String realName = createRequest.get("realName");
        String organization = createRequest.get("organization");
        String role = createRequest.get("role");

        logger.info("管理员 {} 创建用户请求: {}", currentUser, username);

        try {
            Map<String, String> result = userService.createUserByAdmin(currentUser, username, password, email, realName, organization, role);

            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }

            return ResponseEntity.ok(result);
        } catch (SecurityException e) {
            return ResponseEntity.status(403).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("创建用户失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "创建用户失败"));
        }
    }

    /**
     * 获取所有用户列表（仅管理员）
     */
    @GetMapping("/list")
    public ResponseEntity<?> getAllUsers(HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        try {
            List<User> users = userService.getAllUsers(currentUser);
            return ResponseEntity.ok(Map.of("users", users));
        } catch (SecurityException e) {
            return ResponseEntity.status(403).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "获取用户列表失败"));
        }
    }
    
    /**
     * 更新用户信息（仅管理员）
     */
    @PutMapping("/{username}")
    public ResponseEntity<?> updateUser(
            @PathVariable String username,
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {

        String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }

        String email = request.get("email");
        String realName = request.get("realName");
        String organization = request.get("organization");

        logger.info("管理员 {} 更新用户 {} 的信息", currentUser, username);

        try {
            Map<String, String> result = userService.updateUserInfo(currentUser, username, email, realName, organization);

            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }

            return ResponseEntity.ok(result);
        } catch (SecurityException e) {
            return ResponseEntity.status(403).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "更新用户信息失败"));
        }
    }

    /**
     * 更新用户角色（仅管理员）
     */
    @PutMapping("/{username}/role")
    public ResponseEntity<?> updateUserRole(
            @PathVariable String username,
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);
        String newRoleStr = request.get("role");
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        if (newRoleStr == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "角色参数不能为空"));
        }
        
        try {
            UserRole newRole = UserRole.fromCode(newRoleStr);
            Map<String, String> result = userService.updateUserRole(currentUser, username, newRole);
            
            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户角色失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "更新用户角色失败"));
        }
    }
    
    /**
     * 更新用户状态（仅管理员）
     */
    @PutMapping("/{username}/status")
    public ResponseEntity<?> updateUserStatus(
            @PathVariable String username,
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);
        String newStatus = request.get("status");
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        if (newStatus == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "状态参数不能为空"));
        }
        
        try {
            Map<String, String> result = userService.updateUserStatus(currentUser, username, newStatus);
            
            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "更新用户状态失败"));
        }
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public ResponseEntity<?> getUserInfo(HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }

        try {
            User user = userService.getUserInfo(currentUser);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "用户不存在"));
            }

            return ResponseEntity.ok(user);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "获取用户信息失败"));
        }
    }

    /**
     * 重置用户密码（仅管理员）
     */
    @PostMapping("/{username}/reset-password")
    public ResponseEntity<?> resetPassword(@PathVariable String username, HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }

        logger.info("管理员 {} 重置用户 {} 的密码", currentUser, username);

        try {
            Map<String, String> result = userService.resetUserPassword(currentUser, username);

            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }

            return ResponseEntity.ok(result);
        } catch (SecurityException e) {
            return ResponseEntity.status(403).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("重置密码失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "重置密码失败"));
        }
    }

    /**
     * 修改自己的密码
     */
    @PutMapping("/change-password")
    public ResponseEntity<?> changePassword(@RequestBody Map<String, String> passwordRequest, HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }

        String currentPassword = passwordRequest.get("currentPassword");
        String newPassword = passwordRequest.get("newPassword");

        if (currentPassword == null || currentPassword.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "当前密码不能为空"));
        }

        if (newPassword == null || newPassword.length() < 6) {
            return ResponseEntity.badRequest().body(Map.of("error", "新密码长度不能少于6位"));
        }

        logger.info("用户 {} 请求修改密码", currentUser);

        try {
            Map<String, String> result = userService.changePassword(currentUser, currentPassword, newPassword);

            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("修改密码失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "修改密码失败"));
        }
    }
    

}
