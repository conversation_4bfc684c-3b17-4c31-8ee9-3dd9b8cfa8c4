package com.tcmp2.controller;

import com.tcmp2.service.LoginService;
import com.tcmp2.utils.CookieUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 23:36
 * @description : 登录控制器
 */
@RestController
public class LoginController {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    
    @Resource
    private LoginService loginService;
    
    @Resource
    private CookieUtil cookieUtil;
    
    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody Map<String, String> loginRequest, HttpServletResponse response) {
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        
        if (username == null || password == null) {
            return ResponseEntity.badRequest()
                .body(Map.of("message", "Username and password cannot be empty"));
        }
        
        Map<String, String> result = loginService.login(username, password);
        
        if ("Login successful".equals(result.get("message"))) {
            // 获取JWT令牌
            String token = result.get("token");
            
            // 将令牌存储在HttpOnly cookie中
            cookieUtil.createTokenCookie(response, token);

            // 同时在响应中返回token，支持前端LocalStorage存储（7天过期功能）
            Map<String, String> safeResult = new HashMap<>(result);
            safeResult.put("message", "Login successful");
            safeResult.put("token", token); // 返回token供前端存储

            return ResponseEntity.ok(safeResult);
        } else {
            // 翻译错误信息
            Map<String, String> translatedResult = new HashMap<>(result);
            if ("Invalid username or password".equals(result.get("message"))) {
                translatedResult.put("message", "Invalid username or password");
            }
            return ResponseEntity.status(401).body(translatedResult);
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<?> logout(HttpServletResponse response) {
        // 清除JWT cookie
        cookieUtil.clearTokenCookie(response);
        
        logger.info("用户登出成功，已清除JWT cookie");
        Map<String, String> result = loginService.logout();
        Map<String, String> translatedResult = new HashMap<>(result);
        if ("Logout successful".equals(result.get("message"))) {
            translatedResult.put("message", "Logout successful");
        }
        return ResponseEntity.ok(translatedResult);
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<?> refreshToken(HttpServletRequest request, HttpServletResponse response) {
        // 从cookie中获取令牌
        String token = cookieUtil.getTokenFromCookie(request);
        
        // 如果cookie中没有令牌，尝试从Authorization头中获取
        if (token == null) {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }
        }
        
        if (token == null) {
            logger.warn("刷新令牌失败: 未提供令牌");
            return ResponseEntity.status(401).body(Map.of("message", "No token provided"));
        }
        
        Map<String, String> result = loginService.refreshToken(token);
        Map<String, String> translatedResult = new HashMap<>(result);
        
        String message = result.get("message");
        if ("Token refreshed successfully".equals(message)) {
            translatedResult.put("message", "Token refreshed successfully");
            // 获取新的JWT令牌
            String newToken = result.get("token");
            
            // 更新HttpOnly cookie
            cookieUtil.createTokenCookie(response, newToken);
            
            // 从响应中移除令牌，不通过JSON返回
            translatedResult.remove("token");
            
            logger.info("令牌刷新成功，已更新JWT cookie");
            return ResponseEntity.ok(translatedResult);
        } else if ("Token still valid".equals(message)) {
            translatedResult.put("message", "Token still valid");
            String newToken = result.get("token");
            cookieUtil.createTokenCookie(response, newToken);
            translatedResult.remove("token");
            logger.info("令牌仍然有效，已更新JWT cookie");
            return ResponseEntity.ok(translatedResult);
        } else if ("Invalid token".equals(message)) {
            translatedResult.put("message", "Invalid token");
            logger.warn("刷新令牌失败: 无效的令牌");
            return ResponseEntity.status(401).body(translatedResult);
        } else if ("Token cannot be refreshed".equals(message)) {
            translatedResult.put("message", "Token cannot be refreshed");
            logger.warn("刷新令牌失败: 令牌无法刷新");
            return ResponseEntity.status(400).body(translatedResult);
        } else {
            translatedResult.put("message", "Token refresh failed");
            logger.warn("刷新令牌失败: {}", message);
            return ResponseEntity.status(400).body(translatedResult);
        }
    }
}
