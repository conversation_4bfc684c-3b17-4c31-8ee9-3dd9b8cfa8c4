package com.tcmp2.controller;

import com.tcmp2.service.SearchService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 0:22
 * @description : 材料搜索控制器
 */
@RestController
@RequestMapping("/materials")
public class SearchController {
    
    private static final Logger logger = LoggerFactory.getLogger(SearchController.class);
    
    @Resource
    private SearchService searchService;

    @Resource
    private JwtUtil jwtUtil;

    @GetMapping("/search")
    public Map<String, Object> searchMaterials(@RequestParam Map<String, String> params) {
        logger.info("搜索材料，参数: {}", params);
        return searchService.searchMaterials(params);
    }

    @GetMapping("/my-materials")
    public ResponseEntity<?> getMyMaterials(HttpServletRequest request, @RequestParam Map<String, String> params) {
        try {
            // 从JWT中获取当前用户名
            String currentUser = jwtUtil.extractUsernameFromRequest(request);

            if (currentUser == null) {
                return ResponseEntity.status(401).body("User not authenticated");
            }

            logger.info("获取用户 {} 的材料列表", currentUser);

            // 添加创建者过滤条件
            Map<String, String> searchParams = new HashMap<>(params);
            searchParams.put("createdBy", currentUser);

            Map<String, Object> result = searchService.searchMaterials(searchParams);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取用户材料列表时发生异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }

    @GetMapping("/{material_id}")
    public ResponseEntity<?> getMaterialById(@PathVariable("material_id") String materialId) {
        logger.info("根据ID获取材料: {}", materialId);

        if (materialId == null || materialId.trim().isEmpty()) {
            return ResponseEntity.status(400).body("Material ID cannot be empty");
        }

        try {
            logger.info("开始查询材料: {}", materialId);
            Map<String, Object> material = searchService.getMaterialById(materialId);
            logger.info("查询结果: {}", material == null ? "null" : "found");

            if (material == null) {
                logger.info("材料不存在，返回404");
                return ResponseEntity.status(404).body("Material not found with ID: " + materialId);
            }

            logger.info("成功获取材料详情");
            return ResponseEntity.ok(material);
        } catch (Exception e) {
            logger.error("获取材料详情时发生异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }
}
