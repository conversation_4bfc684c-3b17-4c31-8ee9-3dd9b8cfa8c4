package com.tcmp2.controller;

import com.tcmp2.service.DataExportService;
import com.tcmp2.utils.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.Map;

/**
 * 数据导出控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20
 * @description : 提供数据导出功能的REST API
 */
@RestController
@RequestMapping("/admin")
public class DataExportController {

    private static final Logger logger = LoggerFactory.getLogger(DataExportController.class);

    @Autowired
    private DataExportService dataExportService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 导出所有材料数据
     * 只有管理员可以访问此接口
     */
    @GetMapping("/export/all-materials")
    public ResponseEntity<?> exportAllMaterials(HttpServletRequest request) {
        try {
            // 获取当前用户
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            if (currentUser == null) {
                logger.warn("导出请求缺少用户认证");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "用户未认证"));
            }

            // 检查导出权限
            if (!dataExportService.hasExportPermission(currentUser)) {
                logger.warn("用户 {} 尝试导出数据但没有权限", currentUser);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "您没有导出数据的权限，只有管理员可以执行此操作"));
            }

            logger.info("管理员 {} 开始导出所有材料数据", currentUser);

            // 执行导出
            InputStream exportStream = dataExportService.exportAllMaterialsData(currentUser);
            
            if (exportStream == null) {
                logger.error("导出数据失败，返回的流为空");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "导出失败，请稍后重试"));
            }

            // 生成文件名
            String fileName = dataExportService.getExportFileName();
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
            headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.add("Pragma", "no-cache");
            headers.add("Expires", "0");

            logger.info("开始发送导出文件: {}", fileName);

            // 返回文件流
            return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(exportStream));

        } catch (SecurityException e) {
            logger.warn("权限验证失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("导出数据时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "导出失败: " + e.getMessage()));
        }
    }

    /**
     * 检查导出权限
     * 前端可以调用此接口来决定是否显示导出按钮
     */
    @GetMapping("/export/check-permission")
    public ResponseEntity<?> checkExportPermission(HttpServletRequest request) {
        try {
            // 获取当前用户
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "用户未认证"));
            }

            // 检查权限
            boolean hasPermission = dataExportService.hasExportPermission(currentUser);
            
            return ResponseEntity.ok(Map.of(
                "hasPermission", hasPermission,
                "username", currentUser
            ));

        } catch (Exception e) {
            logger.error("检查导出权限时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "检查权限失败"));
        }
    }

    /**
     * 获取导出状态信息
     * 可以用于显示数据库统计信息
     */
    @GetMapping("/export/info")
    public ResponseEntity<?> getExportInfo(HttpServletRequest request) {
        try {
            // 获取当前用户
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "用户未认证"));
            }

            // 检查权限
            if (!dataExportService.hasExportPermission(currentUser)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "您没有查看导出信息的权限"));
            }

            // 这里可以添加获取数据库统计信息的逻辑
            // 比如材料总数、CIF文件数量等
            Map<String, Object> info = Map.of(
                "message", "导出功能可用",
                "description", "将导出所有材料的JSON数据和对应的CIF文件",
                "format", "ZIP压缩包",
                "includes", new String[]{"材料JSON数据", "CIF结构文件", "导出信息文件"}
            );

            return ResponseEntity.ok(info);

        } catch (Exception e) {
            logger.error("获取导出信息时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "获取信息失败"));
        }
    }
}
