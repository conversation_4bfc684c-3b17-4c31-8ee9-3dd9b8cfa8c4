package com.tcmp2.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcmp2.common.utils.FileValidationUtil;
import com.tcmp2.service.DataCURDService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-05-31 17:00
 * @description : 材料文件管理控制器（处理CIF文件上传等）
 */
@RestController
@RequestMapping("/materials")
public class MaterialFileController {

    private static final Logger logger = LoggerFactory.getLogger(MaterialFileController.class);

    @Resource
    private DataCURDService dataCURDService;

    @Resource
    private JwtUtil jwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();



    /**
     * 新增材料（带CIF文件上传）
     */
    @PostMapping("/add-with-cif")
    public ResponseEntity<?> addMaterialWithCIF(
            HttpServletRequest request,
            @RequestParam("data") String dataJson,
            @RequestParam(value = "cifFile", required = false) MultipartFile cifFile) {
        
        try {
            // 从JWT中获取当前用户名
            String currentUser = jwtUtil.extractUsernameFromRequest(request);

            if (currentUser == null) {
                return ResponseEntity.status(401).body("User not authenticated");
            }

            // 解析JSON数据
            Map<String, Object> data = parseJsonData(dataJson);
            if (data == null) {
                return ResponseEntity.status(400).body("Invalid JSON data");
            }

            // 检查CIF文件
            if (cifFile == null || cifFile.isEmpty()) {
                return ResponseEntity.status(400).body("CIF file is required");
            }

            // 验证CIF文件
            FileValidationUtil.ValidationResult cifValidation = FileValidationUtil.validateCifFile(cifFile);
            if (!cifValidation.isValid()) {
                return ResponseEntity.status(400).body("CIF file validation failed: " + cifValidation.getErrorMessage());
            }

            // 调用服务层方法
            Map<String, Object> result = dataCURDService.addMaterialWithCIF(data, cifFile, currentUser);

            if (result.containsKey("error")) {
                return ResponseEntity.status(400).body(result);
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("新增材料（带CIF文件）时发生异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Internal server error: " + e.getMessage());
            errorResponse.put("details", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * ZIP批量上传材料
     */
    @PostMapping("/batch-upload")
    public ResponseEntity<?> batchUploadMaterials(
            @RequestParam("zipFile") MultipartFile zipFile,
            HttpServletRequest request) {

        try {
            // 从JWT中获取当前用户名
            String currentUser = jwtUtil.extractUsernameFromRequest(request);

            if (currentUser == null) {
                return ResponseEntity.status(401).body("User not authenticated");
            }

            // 检查ZIP文件
            if (zipFile == null || zipFile.isEmpty()) {
                return ResponseEntity.status(400).body("ZIP file is required");
            }

            // 验证ZIP文件
            FileValidationUtil.ValidationResult zipValidation = FileValidationUtil.validateZipFile(zipFile);
            if (!zipValidation.isValid()) {
                return ResponseEntity.status(400).body("ZIP file validation failed: " + zipValidation.getErrorMessage());
            }

            // 调用服务层方法处理ZIP批量上传
            Map<String, Object> result = dataCURDService.batchUploadFromZip(zipFile, currentUser);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("ZIP批量上传时发生异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "ZIP batch upload failed: " + e.getMessage());
            errorResponse.put("details", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 修改材料（带CIF文件上传）
     */
    @PostMapping("/{materialId}/update-with-cif")
    public ResponseEntity<?> updateMaterialWithCIF(
            HttpServletRequest request,
            @PathVariable String materialId,
            @RequestParam("data") String dataJson,
            @RequestParam(value = "cifFile", required = false) MultipartFile cifFile) {
        
        try {
            // 从JWT中获取当前用户名
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            
            if (currentUser == null) {
                return ResponseEntity.status(401).body("User not authenticated");
            }

            // 解析JSON数据
            Map<String, Object> data = parseJsonData(dataJson);
            if (data == null) {
                return ResponseEntity.status(400).body("Invalid JSON data");
            }

            // 如果有CIF文件，验证CIF文件
            if (cifFile != null && !cifFile.isEmpty()) {
                FileValidationUtil.ValidationResult cifValidation = FileValidationUtil.validateCifFile(cifFile);
                if (!cifValidation.isValid()) {
                    return ResponseEntity.status(400).body("CIF file validation failed: " + cifValidation.getErrorMessage());
                }
            }

            // 调用服务层方法
            Map<String, Object> result = dataCURDService.updateMaterialWithCIF(materialId, data, cifFile, currentUser);

            if (result.containsKey("error")) {
                return ResponseEntity.status(400).body(result);
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("修改材料（带CIF文件）时发生异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, Object> parseJsonData(String dataJson) {
        try {
            // 使用Jackson解析JSON
            TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
            return objectMapper.readValue(dataJson, typeRef);

        } catch (Exception e) {
            logger.error("解析JSON数据失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
