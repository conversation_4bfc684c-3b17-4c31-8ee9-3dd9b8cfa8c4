package com.tcmp2.controller;

import com.tcmp2.exception.BusinessException;
import com.tcmp2.service.CIFAndXRDFilesService;
import jakarta.annotation.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR> <PERSON>
 * @Date: 2025/5/14 15:58
 * @Description: 处理腾讯云COS下载的CIF文件并转换为XRD文件
 */
@RestController
public class CIFAndXRDFilesController {

    @Resource
    private CIFAndXRDFilesService cifAndXRDFilesService;

    /**
     * 从COS上下载，获取CIF文件
     * @param materialId
     * @return
     */
    @GetMapping("/{material_id}/cif")
    public String getCIFFile(@PathVariable("material_id") String materialId) throws IOException {
        if (materialId == null || materialId.trim().isEmpty()) {
            throw new BusinessException("Material ID cannot be empty", HttpStatus.BAD_REQUEST);
        }

        return cifAndXRDFilesService.getCIFFile(materialId);
    }

    /**
     * 从COS上下载，获取3个XRD文件
     * @param materialId
     * @return
     */
    @GetMapping("/{material_id}/xrd")
    public ResponseEntity<byte[]> getXRDFile(@PathVariable("material_id") String materialId) throws IOException {
        if (materialId == null || materialId.trim().isEmpty()) {
            throw new BusinessException("Material ID cannot be empty", HttpStatus.BAD_REQUEST);
        }
        return cifAndXRDFilesService.getXRDFile(materialId);
    }
}
