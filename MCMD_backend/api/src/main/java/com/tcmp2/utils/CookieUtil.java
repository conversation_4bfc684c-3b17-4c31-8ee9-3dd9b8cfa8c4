package com.tcmp2.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-03-02 19:53
 * @description :Cookie工具类
 * 用于处理JWT令牌的cookie操作
 */
@Component
public class CookieUtil {

    // Cookie名称
    public static final String JWT_COOKIE_NAME = "jwt_token";
    
    // Cookie有效期（秒）
    private static final int COOKIE_MAX_AGE = 24 * 60 * 60; // 24小时
    
    // Cookie路径
    private static final String COOKIE_PATH = "/";
    
    @Value("${cookie.secure:false}")
    private boolean secureCookie;
    
    @Value("${cookie.domain:}")
    private String cookieDomain;
    
    /**
     * 创建JWT令牌的cookie
     *
     * @param response HTTP响应
     * @param token JWT令牌
     */
    public void createTokenCookie(HttpServletResponse response, String token) {
        Cookie cookie = new Cookie(JWT_COOKIE_NAME, token);
        cookie.setHttpOnly(false); // 临时设置为false以便调试，生产环境应设置为true
        cookie.setSecure(secureCookie); // 在生产环境中应设置为true，要求HTTPS
        cookie.setPath(COOKIE_PATH);
        cookie.setMaxAge(COOKIE_MAX_AGE);

        if (cookieDomain != null && !cookieDomain.isEmpty()) {
            cookie.setDomain(cookieDomain);
        }

        response.addCookie(cookie);
    }
    
    /**
     * 从请求中获取JWT令牌
     *
     * @param request HTTP请求
     * @return JWT令牌，如果不存在则返回null
     */
    public String getTokenFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (JWT_COOKIE_NAME.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }

        // 如果Cookie中没有，尝试从URL参数中获取（用于EventSource等场景）
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.trim().isEmpty()) {
            return tokenParam;
        }

        return null;
    }
    
    /**
     * 清除JWT令牌的cookie
     *
     * @param response HTTP响应
     */
    public void clearTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(JWT_COOKIE_NAME, null);
        cookie.setHttpOnly(true);
        cookie.setSecure(secureCookie);
        cookie.setPath(COOKIE_PATH);
        cookie.setMaxAge(0); // 立即过期
        
        if (cookieDomain != null && !cookieDomain.isEmpty()) {
            cookie.setDomain(cookieDomain);
        }
        
        response.addCookie(cookie);
    }
} 