package com.tcmp2.exception;

import com.tcmp2.common.utils.ErrorMessageUtil;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import io.jsonwebtoken.UnsupportedJwtException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.async.AsyncRequestNotUsableException;
import org.apache.catalina.connector.ClientAbortException;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * <AUTHOR> Zhang Chengwei
 * @createDate : 2025-03-02 19:53
 * @description :全局异常处理器
 * 用于统一处理应用程序中的各种异常，提供友好的错误响应
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Map<String, Object> handleException(Exception e, HttpServletRequest request) {
        // 检查是否是SSE请求，如果是则不处理，让具体的控制器处理
        String accept = request.getHeader("Accept");
        String contentType = request.getContentType();

        if ((accept != null && accept.contains("text/event-stream")) ||
            (contentType != null && contentType.contains("text/event-stream"))) {
            // 对于SSE请求，不在全局异常处理器中处理，避免Content-Type冲突
            logger.debug("SSE请求异常，由具体控制器处理: {}", e.getMessage());
            return null;
        }

        // 检查是否是客户端断开连接的异常
        if (isClientDisconnectionException(e)) {
            logger.debug("客户端断开连接: {}", e.getMessage());
            return null;
        }

        logger.error("未处理的异常", e);
        return ErrorMessageUtil.createErrorResponse(e);
    }

    /**
     * 处理数据库访问异常
     */
    @ExceptionHandler(DataAccessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Map<String, Object> handleDataAccessException(DataAccessException e) {
        logger.error("数据库访问异常", e);
        return ErrorMessageUtil.createErrorResponse(ErrorMessageUtil.ERROR_TYPE_DATABASE,
            "数据库操作失败，请稍后重试");
    }

    /**
     * 处理请求参数缺失异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Map<String, String> handleMissingParams(MissingServletRequestParameterException e) {
        logger.warn("请求参数缺失: {}", e.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", "Missing required parameter: " + e.getParameterName());
        return response;
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Map<String, String> handleMethodNotSupported(HttpRequestMethodNotSupportedException e) {
        logger.warn("不支持的请求方法: {}", e.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", "Method not supported: " + e.getMethod());
        return response;
    }

    /**
     * 处理请求体解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Map<String, String> handleMessageNotReadable(HttpMessageNotReadableException e) {
        logger.warn("请求体解析失败: {}", e.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", "Invalid request format");
        return response;
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Map<String, String> handleTypeMismatch(MethodArgumentTypeMismatchException e) {
        logger.warn("参数类型不匹配: {}", e.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", "Invalid parameter type: " + e.getName());
        return response;
    }

    /**
     * 处理JWT相关异常
     */
    @ExceptionHandler({
            ExpiredJwtException.class,
            UnsupportedJwtException.class,
            MalformedJwtException.class,
            SignatureException.class,
            IllegalArgumentException.class
    })
    public ResponseEntity<Map<String, String>> handleJwtException(Exception e) {
        logger.warn("JWT验证失败: {}", e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        HttpStatus status = HttpStatus.UNAUTHORIZED;
        
        if (e instanceof ExpiredJwtException) {
            response.put("message", "Login session expired, please login again");
        } else if (e instanceof UnsupportedJwtException) {
            response.put("message", "Unsupported token format");
        } else if (e instanceof MalformedJwtException || e instanceof SignatureException) {
            response.put("message", "Invalid token");
        } else {
            response.put("message", "Token verification failed");
        }
        
        return new ResponseEntity<>(response, status);
    }

    /**
     * 处理参数验证异常（@Valid注解）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Map<String, Object> handleValidationException(MethodArgumentNotValidException e) {
        logger.warn("参数验证失败: {}", e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("message", "参数验证失败");

        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        response.put("errors", errors);

        return response;
    }

    /**
     * 处理路径参数验证异常（@Validated注解）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Map<String, Object> handleConstraintViolationException(ConstraintViolationException e) {
        logger.warn("路径参数验证失败: {}", e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("message", "参数验证失败");

        String errors = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        response.put("errors", errors);

        return response;
    }

    /**
     * 处理SSE客户端断开连接异常
     */
    @ExceptionHandler({
            AsyncRequestNotUsableException.class,
            ClientAbortException.class
    })
    public void handleClientDisconnection(Exception e) {
        // 客户端断开连接是正常情况，只记录DEBUG级别日志，不作为错误处理
        logger.debug("客户端断开连接: {}", e.getMessage());
        // 不返回任何响应，因为客户端已经断开连接
    }

    /**
     * 处理IO异常（包括Broken pipe）
     */
    @ExceptionHandler(IOException.class)
    public void handleIOException(IOException e) {
        // IO异常通常是客户端断开连接导致的，记录DEBUG级别日志
        if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
            logger.debug("客户端连接中断: {}", e.getMessage());
        } else {
            logger.warn("IO异常: {}", e.getMessage());
        }
        // 不返回响应，避免在SSE流中出现格式错误
    }

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Map<String, String>> handleBusinessException(BusinessException e) {
        logger.warn("业务异常: {}", e.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", e.getMessage());
        return new ResponseEntity<>(response, e.getStatus());
    }

    /**
     * 检查是否是客户端断开连接的异常
     */
    private boolean isClientDisconnectionException(Exception e) {
        if (e == null) return false;

        String message = e.getMessage();
        if (message == null) return false;

        // 检查常见的客户端断开连接异常
        return e instanceof AsyncRequestNotUsableException ||
               e instanceof ClientAbortException ||
               e instanceof IOException && (
                   message.contains("Broken pipe") ||
                   message.contains("Connection reset") ||
                   message.contains("flush")
               );
    }
}