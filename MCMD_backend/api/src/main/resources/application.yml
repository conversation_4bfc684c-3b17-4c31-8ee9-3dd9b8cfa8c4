server:
  port: 8081
spring:
  data:
    mongodb:
      uri: ${MONGODB_URI}
      # 连接池配置
      options:
        max-connection-pool-size: 100
        min-connection-pool-size: 10
        max-connection-idle-time: 60000
        max-connection-life-time: 120000
        connect-timeout: 10000
        socket-timeout: 30000
        server-selection-timeout: 30000
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration

# 腾讯云COS配置
tencent:
  cos:
    secretId: ${TENCENT_COS_SECRET_ID}
    secretKey: ${TENCENT_COS_SECRET_KEY}
    bucketName: ${TENCENT_COS_BUCKET_NAME}
    region: ${TENCENT_COS_REGION}
    domain: ${TENCENT_COS_DOMAIN}

# CORS配置
cors:
  # 允许的源地址，多个地址用逗号分隔
  # 开发环境示例：http://localhost:8080,http://127.0.0.1:8080
  # 生产环境示例：https://yourdomain.com,https://www.yourdomain.com
  allowed-origins: ${CORS_ALLOWED_ORIGINS}

# Cookie配置
cookie:
  secure: ${COOKIE_SECURE:false}
  domain: ${COOKIE_DOMAIN:}

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时（毫秒）
  refresh-window: ${JWT_REFRESH_WINDOW:21600000} # 6小时（毫秒）

# LangChain4J配置
langchain4j:
  open-ai:
   streaming-chat-model:
     api-key: ${LANGCHAIN4J_API_KEY}
     base-url: ${LANGCHAIN4J_BASE_URL}
     model-name: ${LANGCHAIN4J_MODEL_NAME}
     log-requests: true
     log-responses: true
     temperature: 0.7
   chat-model:
     api-key: ${LANGCHAIN4J_API_KEY}
     base-url: ${LANGCHAIN4J_BASE_URL}
     model-name: ${LANGCHAIN4J_MODEL_NAME}
     log-requests: true
     log-responses: true
     temperature: 0.7

# 日志配置
logging:
  level:
    com.tcmp2.exception.GlobalExceptionHandler: DEBUG
    com.tcmp2.controller.MaterialAnalysisController: DEBUG
    org.springframework.web.context.request.async: WARN
    org.apache.catalina.connector: WARN
