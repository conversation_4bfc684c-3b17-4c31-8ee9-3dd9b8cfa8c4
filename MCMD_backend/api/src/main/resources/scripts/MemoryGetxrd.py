import sys
import json
import io
import traceback
import warnings
from mp_api.client import MPRester
from pymatgen.core import Structure
from pymatgen.analysis.diffraction.xrd import XRDCalculator
from pymatgen.symmetry.analyzer import SpacegroupAnalyzer
import numpy as np
import os

# 过滤pymatgen的警告信息
warnings.filterwarnings("ignore", category=UserWarning, module="pymatgen.core.structure")

# 命令行参数处理
if len(sys.argv) < 2:
    print("使用方法: python3 MemoryGetxrd.py [元素名称]")
    sys.exit(1)

# 从命令行获取元素名称
element_name = sys.argv[1]

# 从标准输入读取CIF内容
cif_content = sys.stdin.read()

# 保持原始getxrd.py中的参数完全一致
two_theta_range=(10,90)  # theta 范围
fwhm=0.05                # 展宽
step = 0.01               # 步长

# 直接从getxrd.py复制的函数
def calculate_highres_intensity(theta, pattern, fwhm, profile="lorentzian"):
    """基于 Lorentzian 或 Gaussian 峰型的高密度强度计算"""
    intensity = np.zeros_like(theta)
    
    if profile == "lorentzian":
        # Lorentzian 峰型参数
        gamma = fwhm / 2
        for peak_theta, peak_intensity in zip(pattern.x, pattern.y):
            intensity += peak_intensity * gamma / (np.pi * ((theta - peak_theta)**2 + gamma**2))
    
    elif profile == "gaussian":
        # Gaussian 峰型参数
        sigma = fwhm / (2 * np.sqrt(2 * np.log(2)))  # 将 FWHM 转换为 Sigma
        normalization = 1 / (sigma * np.sqrt(2 * np.pi))
        for peak_theta, peak_intensity in zip(pattern.x, pattern.y):
            exponent = -((theta - peak_theta)**2) / (2 * sigma**2)
            intensity += peak_intensity * normalization * np.exp(exponent)
    
    else:
        raise ValueError("Unsupported profile. Use 'lorentzian' or 'gaussian'.")
    
    return intensity

try:
    # 检查CIF内容是否为空
    if not cif_content or cif_content.strip() == "":
        raise ValueError("CIF内容为空，无法处理")
        
    cif_stream = io.StringIO(cif_content)
    
    try:
        structure = Structure.from_str(cif_content, fmt="cif")
    except Exception as struct_err:
        traceback.print_exc(file=sys.stderr)
        raise
    
    # 存储结果
    results = []
    
    # 与getxrd.py保持一致的波长数组
    wavelengths = [1.54056, 1.54184, 1.54439]
    
    for wavelength in wavelengths:
        # 使用当前波长创建计算器
        calculator = XRDCalculator(wavelength=wavelength)
        
        pattern = calculator.get_pattern(structure, two_theta_range)
        
        # 生成展宽数据
        theta = np.arange(*two_theta_range, step=0.01)
        
        intensity = calculate_highres_intensity(theta, pattern, fwhm)
        
        # 构建包含波长的文件名
        wavelength_str = f"{wavelength:.5f}"
        output_filename = f"{element_name}_{wavelength_str}.xy"
        
        # 创建XY数据字符串
        data_buffer = io.StringIO()
        np.savetxt(
            data_buffer,
            np.column_stack((theta, intensity)),
            fmt="%.4f",
            delimiter="\t"
        )
        
        # 获取数据内容
        content = data_buffer.getvalue()
        
        # 将缓冲区内容添加到结果列表
        results.append({
            "filename": output_filename,
            "content": content
        })
    
    # 将结果以JSON格式输出到标准输出
    json_output = json.dumps(results)
    
    # 向标准输出输出结果
    print(json_output)
    
except Exception as e:
    traceback.print_exc(file=sys.stderr)
    
    # 向Java程序返回错误信息
    error_output = json.dumps({"error": str(e), "type": type(e).__name__})
    print(error_output)
    sys.exit(1) 