package com.tcmp2.common.configs;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR> <PERSON>
 * @Date: 2025/5/14 16:14
 * @Description: 腾讯云COS配置类 - 改进的线程安全单例实现
 */
@Configuration
@Data
public class TencentCOSConfig {

    private static final Logger logger = LoggerFactory.getLogger(TencentCOSConfig.class);

    @Value("${tencent.cos.secretId}")
    private String secretId;
    @Value("${tencent.cos.secretKey}")
    private String secretKey;
    @Value("${tencent.cos.bucketName}")
    private String bucketName;
    @Value("${tencent.cos.region}")
    private String region;
    @Value("${tencent.cos.domain}")
    private String domain;

    // 使用AtomicReference确保线程安全的单例
    private final AtomicReference<COSClient> cosClientRef = new AtomicReference<>();

    /**
     * 创建COSClient实例 - 改进的线程安全实现
     */
    @Bean
    public COSClient createCOSClient() {
        return getCOSClientInstance();
    }

    /**
     * 获取COSClient实例 - 线程安全的单例模式
     */
    public COSClient getCOSClientInstance() {
        COSClient client = cosClientRef.get();
        if (client == null) {
            synchronized (this) {
                client = cosClientRef.get();
                if (client == null) {
                    client = createNewCOSClient();
                    cosClientRef.set(client);
                    logger.info("创建新的COSClient实例");
                }
            }
        }
        return client;
    }

    /**
     * 创建新的COSClient实例
     */
    private COSClient createNewCOSClient() {
        try {
            // 设置用户身份信息
            COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);

            // ClientConfig 中包含了后续请求 COS 的客户端设置
            ClientConfig clientConfig = new ClientConfig();

            // 设置 bucket 的地域
            clientConfig.setRegion(new Region(region));

            // 设置请求超时时间
            clientConfig.setSocketTimeout(30 * 1000);
            clientConfig.setConnectionTimeout(30 * 1000);

            // 设置连接池大小
            clientConfig.setMaxConnectionsCount(100);

            // 生成 cos 客户端
            return new COSClient(cred, clientConfig);
        } catch (Exception e) {
            logger.error("创建COSClient失败", e);
            throw new RuntimeException("Failed to create COS client", e);
        }
    }

    /**
     * 应用关闭时安全关闭COSClient
     */
    @PreDestroy
    public void destroy() {
        COSClient client = cosClientRef.getAndSet(null);
        if (client != null) {
            try {
                logger.info("正在关闭COSClient连接池...");
                client.shutdown();
                logger.info("COSClient连接池已关闭");
            } catch (Exception e) {
                logger.error("关闭COSClient时发生异常", e);
            }
        }
    }
}
