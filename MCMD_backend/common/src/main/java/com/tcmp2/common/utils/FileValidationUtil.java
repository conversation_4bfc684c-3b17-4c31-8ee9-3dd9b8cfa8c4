package com.tcmp2.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件验证工具类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 15:00
 * @description : 提供文件上传安全验证功能
 */
public class FileValidationUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileValidationUtil.class);

    // 允许的CIF文件MIME类型
    private static final List<String> ALLOWED_CIF_MIME_TYPES = Arrays.asList(
        "chemical/x-cif",
        "text/plain",
        "application/octet-stream"
    );

    // 允许的ZIP文件MIME类型
    private static final List<String> ALLOWED_ZIP_MIME_TYPES = Arrays.asList(
        "application/zip",
        "application/x-zip-compressed",
        "application/octet-stream"
    );

    // 允许的JSON文件MIME类型
    private static final List<String> ALLOWED_JSON_MIME_TYPES = Arrays.asList(
        "application/json",
        "text/plain",
        "application/octet-stream"
    );

    // 文件大小限制（字节）
    private static final long MAX_CIF_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final long MAX_ZIP_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    private static final long MAX_JSON_FILE_SIZE = 5 * 1024 * 1024; // 5MB

    /**
     * 验证CIF文件
     */
    public static ValidationResult validateCifFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return ValidationResult.error("CIF文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_CIF_FILE_SIZE) {
            return ValidationResult.error("CIF文件大小不能超过10MB");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".cif")) {
            return ValidationResult.error("文件必须是.cif格式");
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && !ALLOWED_CIF_MIME_TYPES.contains(contentType)) {
            logger.warn("CIF文件MIME类型可能不正确: {}", contentType);
        }

        // 检查文件内容（基本CIF格式验证）
        try {
            String content = new String(file.getBytes());
            if (!isValidCifContent(content)) {
                return ValidationResult.error("文件内容不符合CIF格式要求");
            }
        } catch (IOException e) {
            logger.error("读取CIF文件内容失败", e);
            return ValidationResult.error("无法读取文件内容");
        }

        return ValidationResult.success();
    }

    /**
     * 验证ZIP文件
     */
    public static ValidationResult validateZipFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return ValidationResult.error("ZIP文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_ZIP_FILE_SIZE) {
            return ValidationResult.error("ZIP文件大小不能超过100MB");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
            return ValidationResult.error("文件必须是.zip格式");
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && !ALLOWED_ZIP_MIME_TYPES.contains(contentType)) {
            return ValidationResult.error("不支持的文件类型: " + contentType);
        }

        return ValidationResult.success();
    }

    /**
     * 验证JSON文件
     */
    public static ValidationResult validateJsonFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return ValidationResult.error("JSON文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_JSON_FILE_SIZE) {
            return ValidationResult.error("JSON文件大小不能超过5MB");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".json")) {
            return ValidationResult.error("文件必须是.json格式");
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && !ALLOWED_JSON_MIME_TYPES.contains(contentType)) {
            logger.warn("JSON文件MIME类型可能不正确: {}", contentType);
        }

        return ValidationResult.success();
    }

    /**
     * 基本的CIF文件内容验证
     */
    private static boolean isValidCifContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含CIF文件的基本标识
        String lowerContent = content.toLowerCase();
        return lowerContent.contains("data_") || 
               lowerContent.contains("_cell_length_a") ||
               lowerContent.contains("_symmetry_space_group_name_h-m");
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;

        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
