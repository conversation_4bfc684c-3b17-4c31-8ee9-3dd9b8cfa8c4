# MCMD 安全配置指南

## 概述

本文档提供了MCMD系统的安全配置指南，包括生产环境部署的安全最佳实践。

## 1. JWT 安全配置

### 1.1 JWT 密钥管理

**生产环境配置：**
```bash
# 生成强密钥（至少256位）
JWT_SECRET=$(openssl rand -base64 32)
```

**配置要求：**
- JWT密钥长度至少256位
- 使用随机生成的密钥，不要使用默认值
- 定期轮换JWT密钥（建议每6个月）
- 将密钥存储在环境变量中，不要硬编码

### 1.2 JWT 过期时间

```yaml
jwt:
  expiration: 86400000    # 24小时（毫秒）
  refresh-window: 21600000 # 6小时（毫秒）
```

## 2. 文件上传安全

### 2.1 文件类型验证

系统已实现以下文件验证：
- CIF文件：最大10MB，验证文件格式
- ZIP文件：最大100MB，验证压缩包格式
- JSON文件：最大5MB，验证JSON格式

### 2.2 文件内容验证

- CIF文件内容格式验证
- 恶意文件检测
- 文件大小限制

## 3. 数据库安全

### 3.1 MongoDB 连接安全

```yaml
spring:
  data:
    mongodb:
      uri: ********************************:port/database?authSource=admin&ssl=true
```

**安全要求：**
- 使用强密码
- 启用SSL/TLS连接
- 限制数据库访问IP
- 定期备份数据

### 3.2 查询注入防护

- 使用Spring Data MongoDB的安全查询方法
- 参数化查询
- 输入验证和清理

## 4. API 安全

### 4.1 认证和授权

- JWT令牌认证
- 基于角色的访问控制（RBAC）
- API端点权限验证

### 4.2 输入验证

```java
@Validated
public class DataCURDController {
    
    @PostMapping("/update_material/{materialId}")
    public ResponseEntity<?> updateMaterial(
        @PathVariable @NotBlank @Size(max = 100) String materialId,
        @RequestBody @Valid MaterialRequest data) {
        // ...
    }
}
```

### 4.3 CORS 配置

```yaml
cors:
  allowed-origins: https://yourdomain.com
```

## 5. 生产环境部署

### 5.1 环境变量配置

复制 `.env.example` 到 `.env` 并配置实际值：

```bash
cp .env.example .env
# 编辑 .env 文件，填入实际配置值
```

### 5.2 HTTPS 配置

**强制要求：**
- 生产环境必须使用HTTPS
- 配置SSL证书
- 启用HSTS头

### 5.3 Cookie 安全

```yaml
cookie:
  secure: true          # 仅HTTPS传输
  domain: yourdomain.com # 限制域名
```

## 6. 监控和日志

### 6.1 安全日志

- 登录失败记录
- 异常访问监控
- 文件上传日志
- API调用审计

### 6.2 错误处理

- 不暴露敏感信息
- 统一错误响应格式
- 详细错误日志记录

## 7. 定期安全检查

### 7.1 依赖更新

```bash
# 检查依赖漏洞
mvn dependency-check:check

# 更新依赖
mvn versions:display-dependency-updates
```

### 7.2 安全扫描

- 定期进行安全扫描
- 检查已知漏洞
- 更新安全补丁

## 8. 应急响应

### 8.1 安全事件处理

1. 立即隔离受影响系统
2. 分析安全事件
3. 修复安全漏洞
4. 更新安全策略

### 8.2 数据泄露响应

1. 评估泄露范围
2. 通知相关用户
3. 加强安全措施
4. 法律合规处理

## 9. 安全检查清单

- [ ] JWT密钥已更换为强密钥
- [ ] 数据库连接使用SSL
- [ ] 文件上传验证已启用
- [ ] HTTPS已配置
- [ ] CORS已正确配置
- [ ] 输入验证已实现
- [ ] 错误处理不暴露敏感信息
- [ ] 日志记录已配置
- [ ] 依赖已更新到最新版本
- [ ] 安全扫描已完成

## 联系信息

如发现安全问题，请联系：
- 邮箱：<EMAIL>
- 紧急联系：+86-xxx-xxxx-xxxx
