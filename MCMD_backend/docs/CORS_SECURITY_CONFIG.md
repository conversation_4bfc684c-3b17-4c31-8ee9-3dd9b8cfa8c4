# CORS 安全配置指南

## 概述

本文档说明如何安全地配置MCMD项目的CORS（跨域资源共享）设置。

## 配置修改说明

### 1. 移除重复配置

已移除 `common/src/main/java/com/tcmp2/common/config/CorsConfig.java` 中的CORS配置，避免与 `WebConfig.java` 中的配置冲突。

### 2. 增强的安全配置

在 `api/src/main/java/com/tcmp2/filter/WebConfig.java` 中实现了以下安全增强：

#### 源地址验证
- 验证URL格式的合法性
- 禁止使用通配符（*）
- 对非HTTPS协议发出警告
- 支持本地开发环境的HTTP协议

#### 严格的方法和头部限制
- 只允许必要的HTTP方法：GET, POST, PUT, DELETE, OPTIONS
- 限制允许的请求头，避免安全风险
- 控制暴露给前端的响应头

## 环境配置

### 开发环境

在 `application.yml` 中：
```yaml
cors:
  allowed-origins: http://localhost:8080,http://127.0.0.1:8080
```

### 生产环境

使用环境变量：
```bash
export CORS_ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
```

或在 `application.yml` 中：
```yaml
cors:
  allowed-origins: https://yourdomain.com,https://www.yourdomain.com
```

## 安全最佳实践

### 1. 域名限制
- **永远不要使用** `*` 作为允许的源
- 明确指定所有需要访问的域名
- 使用HTTPS协议（生产环境）

### 2. 方法限制
- 只允许应用实际需要的HTTP方法
- 避免允许所有方法（`*`）

### 3. 头部控制
- 限制允许的请求头
- 谨慎暴露响应头
- 避免暴露敏感信息

### 4. 预检缓存
- 设置合理的预检请求缓存时间
- 当前设置为3600秒（1小时）

## 验证配置

### 1. 检查日志
启动应用后，检查日志中是否有CORS相关的警告信息：
```
WARN - 忽略无效的CORS源配置: invalid-url
WARN - 未配置有效的CORS源，使用默认开发环境配置
```

### 2. 浏览器测试
在浏览器开发者工具的Network标签中检查：
- OPTIONS预检请求是否成功
- 响应头中是否包含正确的CORS头部
- 是否有CORS相关的错误信息

### 3. 安全扫描
定期使用安全扫描工具检查CORS配置是否存在安全风险。

## 常见问题

### Q: 为什么移除了原来的CorsConfig类？
A: 原配置使用了不安全的通配符（`*`），且与WebConfig中的配置冲突。

### Q: 如何添加新的允许域名？
A: 在环境变量CORS_ALLOWED_ORIGINS中添加，多个域名用逗号分隔。

### Q: 生产环境是否必须使用HTTPS？
A: 强烈建议使用HTTPS，HTTP协议存在安全风险。

### Q: 如何调试CORS问题？
A: 检查浏览器控制台的错误信息，查看服务器日志，确认配置是否正确。

## 相关文件

- `api/src/main/java/com/tcmp2/filter/WebConfig.java` - 主要CORS配置
- `api/src/main/resources/application.yml` - 配置参数
- `common/src/main/java/com/tcmp2/common/config/CorsConfig.java` - 已废弃

## 更新日志

- 2025-01-XX: 移除重复配置，增强安全验证
- 2025-01-XX: 添加URL格式验证和环境变量支持
