# SSE 安全认证指南

## 问题描述

当前SSE（Server-Sent Events）连接通过URL参数传递JWT令牌，存在以下安全风险：

```
2025-06-08T11:49:47.237+08:00  WARN 63983 --- [nio-8081-exec-6] com.tcmp2.utils.JwtUtil                   : 从URL参数中获取token，路径: /materials/MCMD-5/analyze - 存在安全风险，建议使用其他方式
```

### 安全风险分析

1. **日志泄露**：URL参数会被记录在服务器访问日志中
2. **浏览器历史**：令牌会保存在浏览器历史记录中
3. **引用头泄露**：通过Referer头可能泄露给第三方网站
4. **缓存风险**：代理服务器可能缓存包含令牌的URL
5. **分享风险**：用户可能无意中分享包含令牌的URL

## 解决方案

### 方案1：使用Cookie认证（推荐）

EventSource会自动发送Cookie，这是最安全的方式：

```javascript
// 前端代码
const eventSource = new EventSource('/api/materials/MCMD-5/analyze');
// Cookie会自动发送，无需手动处理
```

**后端已支持Cookie认证**：
```java
// JwtUtil.java 中已实现Cookie令牌提取
if ("jwt_token".equals(cookie.getName())) {
    String token = cookie.getValue();
    return token;
}
```

### 方案2：两步认证（最安全）

1. **第一步**：获取短期令牌
```javascript
// 前端先获取短期令牌
const response = await fetch('/api/materials/MCMD-5/analyze/token', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
    }
});
const { token } = await response.json();
```

2. **第二步**：使用短期令牌建立SSE连接
```javascript
// 使用短期令牌（5分钟有效期）
const eventSource = new EventSource(`/api/materials/MCMD-5/analyze?token=${token}`);
```

### 方案3：自定义Header（技术限制）

EventSource不支持自定义Header，但可以通过其他方式：

```javascript
// 使用Last-Event-ID传递令牌（hack方式）
const eventSource = new EventSource('/api/materials/MCMD-5/analyze');
eventSource.addEventListener('open', function() {
    // 这种方式有技术限制，不推荐
});
```

## 当前实现状态

### 已实现的安全措施

1. **Cookie优先**：系统优先从Cookie中读取令牌
2. **路径限制**：只有特定路径允许URL参数传递令牌
3. **警告日志**：URL参数传递会记录警告日志
4. **短期令牌**：提供了短期令牌生成接口

### 令牌提取优先级

```java
// JwtUtil.extractTokenFromRequest() 的优先级：
1. Authorization Header: "Bearer <token>"
2. Cookie: "jwt_token=<token>"
3. Last-Event-ID Header: "Bearer <token>" (实验性)
4. URL Parameter: "?token=<token>" (仅特定路径，有安全警告)
```

## 推荐的迁移方案

### 立即可行的方案：使用Cookie

**前端修改**：
```javascript
// 确保登录时设置Cookie
document.cookie = `jwt_token=${token}; path=/; secure; httpOnly`;

// SSE连接无需修改，Cookie会自动发送
const eventSource = new EventSource('/api/materials/MCMD-5/analyze');
```

**优点**：
- ✅ 无需修改SSE连接代码
- ✅ 自动发送，无需手动处理
- ✅ 相对安全（不在URL中）
- ✅ 后端已支持

### 长期安全方案：两步认证

**前端修改**：
```javascript
async function startAnalysis(materialId) {
    try {
        // 1. 获取短期令牌
        const tokenResponse = await fetch(`/api/materials/${materialId}/analyze/token`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!tokenResponse.ok) {
            throw new Error('获取分析令牌失败');
        }
        
        const { token } = await tokenResponse.json();
        
        // 2. 使用短期令牌建立SSE连接
        const eventSource = new EventSource(`/api/materials/${materialId}/analyze?token=${token}`);
        
        eventSource.onmessage = function(event) {
            // 处理分析数据
            console.log('Analysis data:', event.data);
        };
        
        eventSource.onerror = function(event) {
            console.error('SSE connection error:', event);
            eventSource.close();
        };
        
    } catch (error) {
        console.error('启动分析失败:', error);
    }
}
```

**优点**：
- ✅ 最高安全性
- ✅ 短期令牌（5分钟有效期）
- ✅ 令牌与特定材料绑定
- ✅ 可审计的令牌使用

## 配置建议

### 生产环境配置

```yaml
# application.yml
logging:
  level:
    com.tcmp2.utils.JwtUtil: WARN  # 确保安全警告可见

# 考虑完全禁用URL参数传递
security:
  jwt:
    allow-url-parameter: false  # 未来版本可添加此配置
```

### 监控和告警

```bash
# 监控安全警告日志
tail -f logs/application.log | grep "存在安全风险"

# 设置告警规则
# 如果URL参数传递令牌的频率过高，应该触发告警
```

## 迁移时间表

### 阶段1：立即执行（Cookie方案）
- [ ] 前端确保登录时设置Cookie
- [ ] 测试SSE连接使用Cookie认证
- [ ] 验证URL参数不再被使用

### 阶段2：中期目标（两步认证）
- [ ] 前端实现两步认证流程
- [ ] 测试短期令牌机制
- [ ] 逐步迁移所有SSE连接

### 阶段3：长期目标（完全移除URL参数）
- [ ] 配置禁用URL参数传递
- [ ] 移除相关代码
- [ ] 安全审计确认

## 安全检查清单

- [ ] 登录时正确设置Cookie
- [ ] SSE连接不使用URL参数
- [ ] 短期令牌机制正常工作
- [ ] 安全日志监控已配置
- [ ] 定期审查令牌使用情况

## 总结

当前的安全警告是正常的，表明系统正在使用不够安全的URL参数传递方式。建议：

1. **立即**：切换到Cookie认证方式
2. **中期**：实现两步认证机制
3. **长期**：完全移除URL参数支持

这样可以在保持功能正常的同时，逐步提升安全性。
