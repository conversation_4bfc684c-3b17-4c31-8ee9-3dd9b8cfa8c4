# MCMD 权限系统文档

## 概述

MCMD（磁热材料数据库）实现了基于角色的权限控制系统（RBAC），确保数据安全和用户权限管理。

## 权限模型

### 用户角色

1. **超级用户（admin）**
   - 可以查询所有数据
   - 可以增删改所有数据（包括其他用户的数据）
   - 可以删除所有材料数据
   - 拥有系统管理权限

2. **普通用户（user）**
   - 可以查询所有数据
   - 只能增删改自己添加的数据
   - 不能修改其他用户的数据
   - 不能执行批量删除操作

### 权限规则

| 操作 | 超级用户 | 普通用户 | 说明 |
|------|----------|----------|------|
| 查询数据 | ✅ | ✅ | 所有用户都可以查询全部数据 |
| 添加数据 | ✅ | ✅ | 所有用户都可以添加数据 |
| 修改数据 | ✅ | ⚠️ | 普通用户只能修改自己创建的数据 |
| 删除数据 | ✅ | ⚠️ | 普通用户只能删除自己创建的数据 |
| 批量删除 | ✅ | ❌ | 只有超级用户可以执行 |

## 数据模型

### 用户表（users）

```javascript
{
  "_id": ObjectId,
  "username": String,      // 用户名（唯一）
  "password": String,      // Argon2哈希密码
  "role": String,          // 角色：admin 或 user
  "email": String,         // 邮箱
  "realName": String,      // 真实姓名
  "organization": String,  // 机构/组织
  "createdAt": Date,       // 创建时间
  "lastLoginAt": Date,     // 最后登录时间
  "status": String         // 账户状态：active, inactive, banned
}
```

### 材料表（materials）

在原有字段基础上新增：

```javascript
{
  // ... 原有字段
  "createdBy": String,        // 数据创建者用户名
  "createdAt": Date,          // 创建时间
  "lastModifiedBy": String,   // 最后修改者用户名
  "lastModifiedAt": Date      // 最后修改时间
}
```

## API 接口

### 权限相关接口

#### 获取当前用户信息
```
GET /auth/user-info
```

响应：
```json
{
  "username": "user123",
  "role": "user",
  "isAdmin": false
}
```

#### 检查材料权限
```
GET /auth/material-permission/{materialId}
```

响应：
```json
{
  "canView": true,
  "isAdmin": false,
  "username": "user123"
}
```

### 数据操作接口

所有增删改接口现在都需要用户认证，并会自动检查权限：

- `POST /add_material` - 添加材料（自动设置创建者）
- `PUT /update_material/{id}` - 更新材料（检查修改权限）
- `DELETE /delete_material/{id}` - 删除材料（检查删除权限）
- `DELETE /delete_all_materials` - 批量删除（仅管理员）

## 前端实现

### 材料详情页面

显示数据贡献者信息：
- 数据贡献者用户名
- 创建时间
- 最后修改者（如果有）
- 最后修改时间（如果有）

### 管理页面

#### 修改数据
- 显示数据贡献者信息
- 根据权限显示/隐藏操作按钮
- 权限不足时显示提示信息

#### 删除数据
- 显示数据贡献者信息
- 权限检查和提示
- 禁用无权限操作的按钮

## 部署和配置

### 1. 数据库迁移

为现有数据添加创建者信息：

```bash
cd MCMD_backend
node scripts/migrate_existing_data.js
```

### 2. 用户管理

#### 创建管理员用户
```bash
node scripts/user_management.js create-admin admin password123 <EMAIL> "管理员" "系统"
```

#### 创建普通用户
```bash
node scripts/user_management.js create-user user1 password123 <EMAIL> "张三" "研究所"
```

#### 更新用户角色
```bash
node scripts/user_management.js update-role user1 admin
```

#### 列出所有用户
```bash
node scripts/user_management.js list-users
```

### 3. 环境配置

确保以下配置正确：

1. **JWT密钥**：系统自动生成，确保在集群部署时使用相同密钥
2. **MongoDB连接**：确保数据库连接正常
3. **前端API配置**：确保前端正确配置后端API地址

## 安全考虑

### 1. 密码安全
- 使用Argon2算法哈希密码
- 密码不会以明文形式存储或传输

### 2. JWT令牌
- 令牌包含用户名和角色信息
- 24小时有效期，支持刷新
- 使用HttpOnly Cookie存储，防止XSS攻击

### 3. 权限验证
- 所有敏感操作都进行服务端权限验证
- 前端权限控制仅用于用户体验优化
- 数据操作前都会检查用户权限

### 4. 数据审计
- 记录数据创建者和修改者
- 记录操作时间戳
- 便于数据溯源和审计

## 故障排除

### 常见问题

1. **用户无法登录**
   - 检查用户名和密码是否正确
   - 确认用户状态为active
   - 检查JWT配置

2. **权限验证失败**
   - 确认JWT令牌有效
   - 检查用户角色设置
   - 验证权限服务配置

3. **数据操作被拒绝**
   - 确认用户有相应权限
   - 检查数据创建者信息
   - 验证权限逻辑

### 日志查看

查看应用日志以获取详细错误信息：

```bash
# 后端日志
tail -f logs/application.log

# 权限相关日志
grep "Authorization" logs/application.log
```

## 更新历史

- **v1.0.0** - 初始权限系统实现
  - 基于角色的权限控制
  - 数据创建者追踪
  - 前端权限显示
  - 用户管理脚本
