# SSE (Server-Sent Events) 异常处理指南

## 问题描述

在使用SSE进行流式响应时，经常会遇到以下异常：

```
java.io.IOException: Broken pipe
org.springframework.web.context.request.async.AsyncRequestNotUsableException
org.apache.catalina.connector.ClientAbortException
```

这些异常通常发生在：
1. 用户关闭浏览器页面
2. 用户刷新页面
3. 网络连接中断
4. 客户端主动断开连接

## 解决方案

### 1. 全局异常处理器优化

在 `GlobalExceptionHandler` 中添加了专门的SSE异常处理：

```java
/**
 * 处理SSE客户端断开连接异常
 */
@ExceptionHandler({
        AsyncRequestNotUsableException.class,
        ClientAbortException.class
})
public void handleClientDisconnection(Exception e) {
    // 客户端断开连接是正常情况，只记录DEBUG级别日志
    logger.debug("客户端断开连接: {}", e.getMessage());
    // 不返回任何响应，因为客户端已经断开连接
}

/**
 * 处理IO异常（包括Broken pipe）
 */
@ExceptionHandler(IOException.class)
public void handleIOException(IOException e) {
    if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
        logger.debug("客户端连接中断: {}", e.getMessage());
    } else {
        logger.warn("IO异常: {}", e.getMessage());
    }
}
```

### 2. 控制器级别的异常处理

在 `MaterialAnalysisController` 中：

```java
try {
    // 发送SSE数据
    emitter.send(SseEmitter.event()
        .name("analysis")
        .data(chunk));
} catch (IOException e) {
    // 客户端断开连接，记录日志并完成emitter
    logger.debug("客户端断开连接，停止发送数据: {}", e.getMessage());
    emitter.completeWithError(e);
    return;
}
```

### 3. 日志级别配置

在 `application.yml` 中配置适当的日志级别：

```yaml
logging:
  level:
    com.tcmp2.exception.GlobalExceptionHandler: DEBUG
    com.tcmp2.controller.MaterialAnalysisController: DEBUG
    org.springframework.web.context.request.async: WARN
    org.apache.catalina.connector: WARN
```

## 最佳实践

### 1. 异常分类处理

- **正常断开连接**：记录DEBUG级别日志，不作为错误处理
- **网络异常**：记录WARN级别日志
- **系统异常**：记录ERROR级别日志

### 2. 资源清理

```java
// 在finally块中确保资源清理
finally {
    if (emitter != null) {
        try {
            emitter.complete();
        } catch (Exception e) {
            logger.debug("清理SSE emitter时发生异常: {}", e.getMessage());
        }
    }
}
```

### 3. 超时处理

```java
SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

emitter.onTimeout(() -> {
    logger.debug("SSE连接超时");
    emitter.complete();
});

emitter.onError((ex) -> {
    logger.debug("SSE连接错误: {}", ex.getMessage());
    emitter.completeWithError(ex);
});
```

### 4. 前端重连机制

```javascript
const eventSource = new EventSource('/api/analysis/stream');

eventSource.onerror = function(event) {
    console.log('SSE连接错误，尝试重连...');
    setTimeout(() => {
        // 重新建立连接
        connectSSE();
    }, 5000);
};
```

## 监控和调试

### 1. 日志监控

监控以下日志模式：
- `客户端断开连接` - 正常情况
- `Broken pipe` - 客户端断开
- `Connection reset` - 网络问题

### 2. 性能监控

- SSE连接数量
- 连接持续时间
- 异常断开率

### 3. 调试技巧

```bash
# 查看SSE相关日志
tail -f logs/application.log | grep -E "(SSE|emitter|Broken pipe)"

# 监控连接状态
netstat -an | grep :8081 | grep ESTABLISHED
```

## 常见问题

### Q: 为什么会出现 "Broken pipe" 异常？

A: 这是正常现象。当客户端（浏览器）关闭连接时，服务器还在尝试发送数据，就会出现这个异常。

### Q: 如何减少这类异常？

A: 
1. 在发送数据前检查连接状态
2. 使用适当的超时设置
3. 实现客户端重连机制
4. 正确处理异常，不要让它们传播到全局异常处理器

### Q: 这些异常会影响系统性能吗？

A: 如果处理得当（如本文档所述），这些异常不会影响系统性能。关键是要将它们识别为正常的客户端断开事件，而不是系统错误。

## 总结

SSE异常处理的核心原则：
1. **区分异常类型**：客户端断开 vs 系统错误
2. **适当的日志级别**：DEBUG用于正常断开，WARN/ERROR用于真正的问题
3. **资源清理**：确保SseEmitter正确关闭
4. **前端重连**：实现自动重连机制
5. **监控告警**：监控真正的系统异常，忽略正常的客户端断开
