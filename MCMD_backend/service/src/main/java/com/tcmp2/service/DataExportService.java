package com.tcmp2.service;

import java.io.InputStream;

/**
 * 数据导出服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20
 * @description : 提供数据导出功能，支持管理员下载全部数据
 */
public interface DataExportService {
    
    /**
     * 导出所有材料数据（JSON + CIF文件）
     * 生成包含所有材料JSON数据和对应CIF文件的ZIP压缩包
     *
     * @param currentUser 当前用户名（必须是管理员）
     * @return 压缩包的输入流，如果失败返回null
     * @throws SecurityException 如果用户不是管理员
     * @throws RuntimeException 如果导出过程中发生错误
     */
    InputStream exportAllMaterialsData(String currentUser);
    
    /**
     * 获取导出文件的建议文件名
     *
     * @return 文件名，格式：MCMD_Export_YYYYMMDD_HHMMSS.zip
     */
    String getExportFileName();
    
    /**
     * 检查用户是否有导出权限
     *
     * @param username 用户名
     * @return true表示有权限，false表示无权限
     */
    boolean hasExportPermission(String username);
}
