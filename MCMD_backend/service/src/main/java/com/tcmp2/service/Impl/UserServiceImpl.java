package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.UserService;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 用户管理服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 10:00
 * @description : 用户注册、管理相关服务实现
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private static final String USERS_COLLECTION = "users";
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Resource
    private AuthorizationService authorizationService;
    
    private final Argon2 argon2 = Argon2Factory.create(Argon2Factory.Argon2Types.ARGON2id);
    
    // 邮箱格式验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    

    
    @Override
    public Map<String, String> createUserByAdmin(String currentUser, String username, String password, String email, String realName, String organization, String role) {
        Map<String, String> response = new HashMap<>();

        try {
            // 检查权限：只有管理员可以创建用户
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "权限不足，只有管理员可以创建用户");
                return response;
            }

            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                response.put("error", "用户名不能为空");
                return response;
            }

            if (password == null || password.length() < 6) {
                response.put("error", "密码长度不能少于6位");
                return response;
            }

            if (email == null || !EMAIL_PATTERN.matcher(email).matches()) {
                response.put("error", "邮箱格式不正确");
                return response;
            }

            // 验证角色
            UserRole userRole;
            try {
                userRole = UserRole.fromCode(role);
            } catch (IllegalArgumentException e) {
                response.put("error", "无效的用户角色");
                return response;
            }

            // 检查用户名是否已存在
            if (isUsernameExists(username.trim())) {
                response.put("error", "用户名已存在");
                return response;
            }

            // 检查邮箱是否已存在
            if (isEmailExists(email.trim())) {
                response.put("error", "邮箱已被注册");
                return response;
            }

            // 创建新用户
            User newUser = new User();
            newUser.setUsername(username.trim());
            newUser.setPassword(argon2.hash(2, 65536, 1, password.toCharArray()));
            newUser.setEmail(email.trim());
            newUser.setRealName(realName != null ? realName.trim() : "");
            newUser.setOrganization(organization != null ? organization.trim() : "");
            newUser.setRole(userRole.getCode());
            newUser.setStatus("active"); // 默认状态为激活
            newUser.setCreatedAt(new Date());

            // 保存用户
            mongoTemplate.save(newUser, USERS_COLLECTION);

            logger.info("管理员 {} 创建用户成功: {}, 角色: {}", currentUser, username, userRole.getCode());
            response.put("message", "用户创建成功");
            response.put("username", username);
            response.put("role", userRole.getCode());

        } catch (Exception e) {
            logger.error("管理员创建用户失败: {}", e.getMessage(), e);
            response.put("error", "创建用户失败，请稍后重试");
        }

        return response;
    }

    @Override
    public List<User> getAllUsers(String currentUser) {
        // 检查权限：只有管理员可以查看所有用户
        if (!authorizationService.isAdmin(currentUser)) {
            throw new SecurityException("权限不足，只有管理员可以查看用户列表");
        }
        
        try {
            Query query = new Query();
            // 不返回密码字段
            query.fields().exclude("password");
            return mongoTemplate.find(query, User.class, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("获取用户列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户列表失败");
        }
    }
    
    @Override
    public Map<String, String> updateUserInfo(String currentUser, String targetUsername, String email, String realName, String organization) {
        Map<String, String> response = new HashMap<>();

        try {
            // 检查权限：只有管理员可以修改用户信息
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "权限不足，只有管理员可以修改用户信息");
                return response;
            }

            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (targetUser == null) {
                response.put("error", "目标用户不存在");
                return response;
            }

            // 防止管理员修改其他管理员的信息
            if ("admin".equals(targetUser.getRole()) && !currentUser.equals(targetUsername)) {
                response.put("error", "不能修改其他管理员的信息");
                return response;
            }

            // 验证邮箱格式
            if (email != null && !email.trim().isEmpty() && !EMAIL_PATTERN.matcher(email.trim()).matches()) {
                response.put("error", "邮箱格式不正确");
                return response;
            }

            // 检查邮箱是否已被其他用户使用
            if (email != null && !email.trim().isEmpty() && !email.trim().equals(targetUser.getEmail())) {
                Query emailQuery = new Query(Criteria.where("email").is(email.trim()).and("username").ne(targetUsername));
                if (mongoTemplate.exists(emailQuery, USERS_COLLECTION)) {
                    response.put("error", "邮箱已被其他用户使用");
                    return response;
                }
            }

            // 更新用户信息
            Update update = new Update().set("lastModifiedAt", new Date());

            if (email != null && !email.trim().isEmpty()) {
                update.set("email", email.trim());
            }
            if (realName != null) {
                update.set("realName", realName.trim());
            }
            if (organization != null) {
                update.set("organization", organization.trim());
            }

            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);

            logger.info("管理员 {} 更新用户 {} 信息成功", currentUser, targetUsername);
            response.put("message", "用户信息更新成功");

        } catch (Exception e) {
            logger.error("更新用户信息失败: {}", e.getMessage(), e);
            response.put("error", "更新用户信息失败，请稍后重试");
        }

        return response;
    }

    @Override
    public Map<String, String> updateUserRole(String currentUser, String targetUsername, UserRole newRole) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // 检查权限：只有管理员可以修改用户角色
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "权限不足，只有管理员可以修改用户角色");
                return response;
            }
            
            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);
            
            if (targetUser == null) {
                response.put("error", "目标用户不存在");
                return response;
            }
            
            // 防止管理员修改自己的角色（避免系统无管理员）
            if (currentUser.equals(targetUsername) && newRole != UserRole.ADMIN) {
                response.put("error", "不能降级自己的管理员权限");
                return response;
            }
            
            // 更新用户角色
            Update update = new Update()
                .set("role", newRole.getCode())
                .set("lastModifiedAt", new Date());
            
            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);
            
            logger.info("管理员 {} 将用户 {} 的角色更新为 {}", currentUser, targetUsername, newRole.getCode());
            response.put("message", "用户角色更新成功");
            response.put("username", targetUsername);
            response.put("newRole", newRole.getCode());
            
        } catch (Exception e) {
            logger.error("更新用户角色失败: {}", e.getMessage(), e);
            response.put("error", "更新用户角色失败");
        }
        
        return response;
    }
    
    @Override
    public Map<String, String> updateUserStatus(String currentUser, String targetUsername, String status) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // 检查权限：只有管理员可以修改用户状态
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "权限不足，只有管理员可以修改用户状态");
                return response;
            }
            
            // 验证状态值 - 只支持 active 和 banned
            if (!status.equals("active") && !status.equals("banned")) {
                response.put("error", "无效的状态值，只支持 active 或 banned");
                return response;
            }
            
            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);
            
            if (targetUser == null) {
                response.put("error", "目标用户不存在");
                return response;
            }
            
            // 防止管理员禁用自己
            if (currentUser.equals(targetUsername) && status.equals("banned")) {
                response.put("error", "不能禁用自己的账户");
                return response;
            }
            
            // 更新用户状态
            Update update = new Update()
                .set("status", status)
                .set("lastModifiedAt", new Date());
            
            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);
            
            logger.info("管理员 {} 将用户 {} 的状态更新为 {}", currentUser, targetUsername, status);
            response.put("message", "用户状态更新成功");
            response.put("username", targetUsername);
            response.put("newStatus", status);
            
        } catch (Exception e) {
            logger.error("更新用户状态失败: {}", e.getMessage(), e);
            response.put("error", "更新用户状态失败");
        }
        
        return response;
    }
    
    @Override
    public User getUserInfo(String username) {
        try {
            Query query = new Query(Criteria.where("username").is(username));
            // 不返回密码字段
            query.fields().exclude("password");
            return mongoTemplate.findOne(query, User.class, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean isUsernameExists(String username) {
        try {
            Query query = new Query(Criteria.where("username").is(username));
            return mongoTemplate.exists(query, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("检查用户名是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean isEmailExists(String email) {
        try {
            Query query = new Query(Criteria.where("email").is(email));
            return mongoTemplate.exists(query, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("检查邮箱是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, String> changePassword(String username, String currentPassword, String newPassword) {
        Map<String, String> response = new HashMap<>();

        try {
            // 获取用户信息
            Query query = new Query(Criteria.where("username").is(username));
            User user = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (user == null) {
                response.put("error", "用户不存在");
                return response;
            }

            // 验证当前密码
            if (!argon2.verify(user.getPassword(), currentPassword.toCharArray())) {
                response.put("error", "当前密码不正确");
                return response;
            }

            // 检查新密码长度
            if (newPassword.length() < 6) {
                response.put("error", "新密码长度不能少于6位");
                return response;
            }

            // 更新密码
            String hashedNewPassword = argon2.hash(2, 65536, 1, newPassword.toCharArray());
            Update update = new Update()
                .set("password", hashedNewPassword)
                .set("lastModifiedAt", new Date());

            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);

            logger.info("用户 {} 密码修改成功", username);
            response.put("message", "密码修改成功");

        } catch (Exception e) {
            logger.error("修改密码失败: {}", e.getMessage(), e);
            response.put("error", "修改密码失败，请稍后重试");
        }

        return response;
    }

    @Override
    public Map<String, String> resetUserPassword(String currentUser, String targetUsername) {
        Map<String, String> response = new HashMap<>();

        try {
            // 检查权限：只有管理员可以重置密码
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "权限不足，只有管理员可以重置用户密码");
                return response;
            }

            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (targetUser == null) {
                response.put("error", "目标用户不存在");
                return response;
            }

            // 防止管理员重置其他管理员的密码
            if ("admin".equals(targetUser.getRole()) && !currentUser.equals(targetUsername)) {
                response.put("error", "不能重置其他管理员的密码");
                return response;
            }

            // 生成新的临时密码（8位随机密码）
            String newPassword = generateRandomPassword();

            // 更新密码
            String hashedNewPassword = argon2.hash(2, 65536, 1, newPassword.toCharArray());
            Update update = new Update()
                .set("password", hashedNewPassword)
                .set("lastModifiedAt", new Date());

            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);

            logger.info("管理员 {} 重置用户 {} 密码成功", currentUser, targetUsername);
            response.put("message", "密码重置成功");
            response.put("newPassword", newPassword);

        } catch (Exception e) {
            logger.error("重置密码失败: {}", e.getMessage(), e);
            response.put("error", "重置密码失败，请稍后重试");
        }

        return response;
    }

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();
        java.util.Random random = new java.util.Random();

        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }

        return password.toString();
    }
}
