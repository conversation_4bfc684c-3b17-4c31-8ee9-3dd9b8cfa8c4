package com.tcmp2.service;

import reactor.core.publisher.Flux;
import java.util.Map;

/**
 * 材料AI分析服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025/5/31
 * @description : 提供材料数据的AI分析功能
 */
public interface MaterialAnalysisService {

    /**
     * 分析材料数据并生成流式响应
     *
     * @param materialData 材料的基本信息（JSON数据）
     * @param cifContent CIF文件内容
     * @return 流式AI分析结果
     */
    Flux<String> analyzeMaterial(Map<String, Object> materialData, String cifContent);

    /**
     * 获取材料的CIF文件内容
     *
     * @param materialId 材料ID
     * @return CIF文件内容
     */
    String getCifContent(String materialId);
}
