package com.tcmp2.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.GetObjectRequest;
import com.tcmp2.common.configs.TencentCOSConfig;
import com.tcmp2.pojo.entity.Material;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.COSFileService;
import com.tcmp2.service.DataExportService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 数据导出服务实现
 *
 * <AUTHOR> <PERSON>wei
 * @createDate : 2025-01-20
 * @description : 实现数据导出功能
 */
@Service
public class DataExportServiceImpl implements DataExportService {

    private static final Logger logger = LoggerFactory.getLogger(DataExportServiceImpl.class);
    private static final String MATERIALS_COLLECTION = "materials";

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AuthorizationService authorizationService;

    @Resource
    private COSFileService cosFileService;

    @Resource
    private TencentCOSConfig tencentCOSConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // COS相关常量
    private static final String BUCKET_NAME = "mat-database-zcw-1311407817";
    private static final String CIF_DIRECTORY = "CIF_files/";

    // COSClient的生命周期管理已移至TencentCOSConfig配置类

    @Override
    public InputStream exportAllMaterialsData(String currentUser) {
        logger.info("开始导出所有材料数据，用户: {}", currentUser);

        // 检查权限
        if (!hasExportPermission(currentUser)) {
            throw new SecurityException("用户没有导出权限: " + currentUser);
        }

        try {
            // 创建临时文件用于存储ZIP
            File tempFile = File.createTempFile("mcmd_export_", ".zip");
            tempFile.deleteOnExit();

            try (FileOutputStream fos = new FileOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {

                // 获取所有材料数据
                Query query = new Query();
                List<Material> materials = mongoTemplate.find(query, Material.class, MATERIALS_COLLECTION);
                logger.info("找到 {} 个材料需要导出", materials.size());

                // 创建导出统计信息
                Map<String, Object> exportInfo = createExportInfo(materials.size(), currentUser);

                // 添加导出信息文件
                addExportInfoToZip(zos, exportInfo);

                // 按照批量导入的格式添加材料数据：每个材料一个文件夹，包含data.json和CIF文件
                addMaterialsInFolderFormat(zos, materials);

                zos.finish();
                logger.info("数据导出完成，文件大小: {} bytes", tempFile.length());
            }

            // 返回文件输入流
            return new FileInputStream(tempFile);

        } catch (Exception e) {
            logger.error("导出数据时发生错误", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getExportFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return "MCMD_Export_" + sdf.format(new Date()) + ".zip";
    }

    @Override
    public boolean hasExportPermission(String username) {
        return authorizationService.isAdmin(username);
    }

    /**
     * 获取COSClient实例 - 使用配置类的线程安全单例
     */
    private COSClient getCOSClient() {
        return tencentCOSConfig.getCOSClientInstance();
    }

    /**
     * 创建导出信息
     */
    private Map<String, Object> createExportInfo(int materialCount, String exportedBy) {
        Map<String, Object> info = new HashMap<>();
        info.put("exportTime", new Date());
        info.put("exportedBy", exportedBy);
        info.put("totalMaterials", materialCount);
        info.put("databaseName", "MCMD - Magnetocaloric Materials Database");
        info.put("version", "1.0");
        info.put("description", "Complete export of all materials data including JSON metadata and CIF structure files");
        return info;
    }

    /**
     * 添加导出信息到ZIP
     */
    private void addExportInfoToZip(ZipOutputStream zos, Map<String, Object> exportInfo) throws IOException {
        ZipEntry entry = new ZipEntry("export_info.json");
        zos.putNextEntry(entry);
        
        String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(exportInfo);
        zos.write(jsonContent.getBytes("UTF-8"));
        zos.closeEntry();
        
        logger.debug("已添加导出信息文件");
    }

    /**
     * 按照批量导入格式添加材料数据到ZIP
     * 每个材料一个文件夹，包含data.json和对应的CIF文件
     */
    private void addMaterialsInFolderFormat(ZipOutputStream zos, List<Material> materials) throws IOException {
        logger.info("开始按文件夹格式添加材料数据到ZIP，总计 {} 个材料", materials.size());

        int successCount = 0;
        int errorCount = 0;
        int cifNotFoundCount = 0;

        for (Material material : materials) {
            try {
                String materialId = material.getId();
                if (materialId == null || materialId.trim().isEmpty()) {
                    logger.warn("材料ID为空，跳过: {}", material.getMongoId());
                    errorCount++;
                    continue;
                }

                // 清理材料ID作为文件夹名
                String folderName = sanitizeFileName(materialId);

                // 1. 创建材料文件夹并添加data.json
                String jsonFileName = folderName + "/data.json";
                ZipEntry jsonEntry = new ZipEntry(jsonFileName);
                zos.putNextEntry(jsonEntry);

                // 转换材料对象为Map以便JSON序列化
                Map<String, Object> materialData = convertMaterialToMap(material);
                String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(materialData);

                zos.write(jsonContent.getBytes("UTF-8"));
                zos.closeEntry();

                // 2. 尝试添加对应的CIF文件
                String formula = material.getFormula();
                if (formula != null && !formula.trim().isEmpty()) {
                    String cifContent = getCifContentFromCOS(formula);

                    if (cifContent != null && !cifContent.trim().isEmpty()) {
                        // 使用化学式作为CIF文件名
                        String cifFileName = folderName + "/" + sanitizeFileName(formula) + ".cif";
                        ZipEntry cifEntry = new ZipEntry(cifFileName);
                        zos.putNextEntry(cifEntry);

                        zos.write(cifContent.getBytes("UTF-8"));
                        zos.closeEntry();

                        logger.debug("成功添加材料 {} 的CIF文件: {}", materialId, formula);
                    } else {
                        logger.debug("材料 {} (化学式: {}) 的CIF文件不存在或为空", materialId, formula);
                        cifNotFoundCount++;
                    }
                } else {
                    logger.debug("材料 {} 没有化学式，跳过CIF文件", materialId);
                    cifNotFoundCount++;
                }

                successCount++;

                if (successCount % 100 == 0) {
                    logger.info("已处理 {} 个材料文件夹", successCount);
                }

            } catch (Exception e) {
                logger.error("处理材料文件夹时发生错误: {}", material.getId(), e);
                errorCount++;
            }
        }

        logger.info("材料文件夹添加完成，成功: {}, 失败: {}, CIF未找到: {}", successCount, errorCount, cifNotFoundCount);
    }

    /**
     * 从COS获取CIF文件内容
     * 使用直接的COS API调用，参考CIFAndXRDFilesServiceImpl的实现
     */
    private String getCifContentFromCOS(String formula) {
        try {
            // 构建COS中的文件路径
            String objectKey = CIF_DIRECTORY + formula + ".cif";

            // 获取COS客户端
            COSClient client = getCOSClient();

            // 创建获取文件请求
            GetObjectRequest getObjectRequest = new GetObjectRequest(BUCKET_NAME, objectKey);

            // 获取文件对象
            COSObject cosObject = client.getObject(getObjectRequest);

            // 获取文件输入流
            InputStream inputStream = cosObject.getObjectContent();

            // 读取文件内容
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, bytesRead);
            }

            // 关闭流
            inputStream.close();

            String content = result.toString("UTF-8");
            logger.debug("成功从COS获取CIF文件内容: {}, 大小: {} bytes", formula, content.length());
            return content;

        } catch (Exception e) {
            logger.debug("从COS获取CIF文件内容失败: {}, 错误: {}", formula, e.getMessage());
            return null;
        }
    }

    /**
     * 转换Material对象为Map
     */
    private Map<String, Object> convertMaterialToMap(Material material) {
        Map<String, Object> map = new HashMap<>();
        
        // 基本信息
        map.put("id", material.getId());
        map.put("Structure", material.getStructure());
        map.put("Formula", material.getFormula());
        
        // 晶格参数
        if (material.getA() != null) map.put("a", material.getA());
        if (material.getB() != null) map.put("b", material.getB());
        if (material.getC() != null) map.put("c", material.getC());
        if (material.getA1() != null) map.put("a1", material.getA1());
        if (material.getB1() != null) map.put("b1", material.getB1());
        if (material.getC1() != null) map.put("c1", material.getC1());
        
        // 空间群信息
        if (material.getSpaceGroupNum() != null) map.put("Space_group_num", material.getSpaceGroupNum());
        if (material.getSpaceGroupSym() != null) map.put("Space_group_sym", material.getSpaceGroupSym());
        if (material.getPointGroup() != null) map.put("Point_group", material.getPointGroup());
        if (material.getCrystalSystem() != null) map.put("Crystal_system", material.getCrystalSystem());
        
        // 磁性信息
        if (material.getMagneticLattice() != null) map.put("magnetic_lattice", material.getMagneticLattice());
        if (material.getMagIon() != null) map.put("Mag_ion", material.getMagIon());
        if (material.getMagIonV() != null) map.put("Mag_ion_V", material.getMagIonV());
        if (material.getMagIonM() != null) map.put("Mag_ion_m", material.getMagIonM());
        if (material.getMagneticMoment() != null) map.put("Magnetic_moment", material.getMagneticMoment());
        if (material.getEffectiveSpin() != null) map.put("Effective_Spin", material.getEffectiveSpin());
        
        // 物理性质
        if (material.getDensity() != null) map.put("Density", material.getDensity());
        if (material.getVUnitCell() != null) map.put("V_unit_cell", material.getVUnitCell());
        if (material.getMoleMass() != null) map.put("Mole_Mass", material.getMoleMass());
        if (material.getMoleVolume() != null) map.put("Mole_Volume", material.getMoleVolume());
        
        // 电子性质
        if (material.getGGABandGap() != null) map.put("GGA_band_gap", material.getGGABandGap());
        if (material.getEHull() != null) map.put("E_hull", material.getEHull());
        
        // 温度相关
        if (material.getTN() != null) map.put("T_N", material.getTN());
        if (material.getTAccess() != null) map.put("T_access", material.getTAccess());
        
        // 其他属性
        if (material.getSGSVol() != null) map.put("S_GS_Vol", material.getSGSVol());
        if (material.getSGSMass() != null) map.put("S_GS_Mass", material.getSGSMass());
        if (material.getNmRln2J1() != null) map.put("Nm_Rln_2J_1_", material.getNmRln2J1());
        if (material.getSGSMol() != null) map.put("S_GS_mol", material.getSGSMol());
        if (material.getSGSMol2() != null) map.put("S_GS_mol2", material.getSGSMol2());
        if (material.getNearestMagIonDistanceInPlane() != null) map.put("Nearest_Mag_Ion_Distance_In_plane", material.getNearestMagIonDistanceInPlane());
        if (material.getNearestMagIonDistanceInterPlane() != null) map.put("Nearest_Mag_Ion_Distance_inter_plane", material.getNearestMagIonDistanceInterPlane());
        if (material.getDoi() != null) map.put("DOI", material.getDoi());
        
        // 元数据
        if (material.getCreatedBy() != null) map.put("createdBy", material.getCreatedBy());
        if (material.getCreatedAt() != null) map.put("createdAt", material.getCreatedAt());
        if (material.getLastModifiedBy() != null) map.put("lastModifiedBy", material.getLastModifiedBy());
        if (material.getLastModifiedAt() != null) map.put("lastModifiedAt", material.getLastModifiedAt());
        
        // 自定义属性
        if (material.getCustomProperties() != null && !material.getCustomProperties().isEmpty()) {
            map.put("customProperties", material.getCustomProperties());
        }
        
        return map;
    }

    /**
     * 清理文件名，移除不安全字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) return "unknown";
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
    }
}
