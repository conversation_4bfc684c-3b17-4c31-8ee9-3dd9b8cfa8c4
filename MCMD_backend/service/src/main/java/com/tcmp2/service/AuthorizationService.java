package com.tcmp2.service;

import com.tcmp2.pojo.entity.Material;

/**
 * 权限验证服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 10:10
 * @description : 处理用户权限验证相关业务
 */
public interface AuthorizationService {
    
    /**
     * 检查用户是否可以修改指定材料
     *
     * @param username 用户名
     * @param material 材料对象
     * @return true表示可以修改，false表示不可以
     */
    boolean canModifyMaterial(String username, Material material);
    
    /**
     * 检查用户是否可以删除指定材料
     *
     * @param username 用户名
     * @param material 材料对象
     * @return true表示可以删除，false表示不可以
     */
    boolean canDeleteMaterial(String username, Material material);
    
    /**
     * 检查用户是否为管理员
     *
     * @param username 用户名
     * @return true表示是管理员，false表示不是
     */
    boolean isAdmin(String username);
    
    /**
     * 获取用户角色
     *
     * @param username 用户名
     * @return 用户角色字符串
     */
    String getUserRole(String username);



    /**
     * 检查用户是否可以添加数据
     *
     * @param username 用户名
     * @return true表示可以添加，false表示不可以
     */
    boolean canAddMaterial(String username);

    /**
     * 检查用户状态是否有效
     *
     * @param username 用户名
     * @return true表示用户状态有效，false表示无效
     */
    boolean isUserActive(String username);
}
