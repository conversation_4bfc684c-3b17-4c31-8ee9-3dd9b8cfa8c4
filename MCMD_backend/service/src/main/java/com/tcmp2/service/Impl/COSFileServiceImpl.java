package com.tcmp2.service.Impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import com.tcmp2.common.configs.TencentCOSConfig;
import com.tcmp2.service.COSFileService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-05-31 16:30
 * @description : 腾讯云COS文件管理服务实现类
 */
@Service
public class COSFileServiceImpl implements COSFileService {
    
    private static final Logger logger = LoggerFactory.getLogger(COSFileServiceImpl.class);
    
    @Resource
    private TencentCOSConfig tencentCOSConfig;

    // 硬编码存储桶名称，与您现有代码保持一致
    private static final String BUCKET_NAME = "mat-database-zcw-1311407817";

    // CIF文件在COS中的目录前缀
    private static final String CIF_DIRECTORY = "CIF_files/";

    /**
     * 获取COSClient实例 - 使用配置类的线程安全单例
     */
    private COSClient getCOSClient() {
        return tencentCOSConfig.getCOSClientInstance();
    }
    
    @Override
    public String uploadCIFFile(MultipartFile file, String formula) {
        if (file == null || file.isEmpty()) {
            logger.error("上传文件为空");
            return null;
        }
        
        if (formula == null || formula.trim().isEmpty()) {
            logger.error("化学式为空");
            return null;
        }
        
        try {
            // 构建COS中的文件路径
            String objectKey = CIF_DIRECTORY + formula + ".cif";
            
            logger.info("开始上传CIF文件到COS: {}", objectKey);
            
            // 获取COS客户端
            COSClient client = getCOSClient();
            
            // 创建文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType("chemical/x-cif"); // CIF文件的MIME类型
            
            // 获取文件输入流
            InputStream inputStream = null;

            try {
                inputStream = file.getInputStream();

                // 创建上传请求
                PutObjectRequest putObjectRequest = new PutObjectRequest(
                    BUCKET_NAME,
                    objectKey,
                    inputStream,
                    metadata
                );

                // 执行上传
                PutObjectResult result = client.putObject(putObjectRequest);

                logger.info("CIF文件上传成功: {}, ETag: {}", objectKey, result.getETag());
                return objectKey;

            } finally {
                // 确保输入流正确关闭
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        logger.warn("关闭输入流时发生异常: {}", e.getMessage());
                    }
                }
            }

        } catch (IOException e) {
            logger.error("读取上传文件失败: {}", e.getMessage(), e);
            return null;
        } catch (CosServiceException e) {
            logger.error("COS服务异常: {}", e.getMessage(), e);
            return null;
        } catch (CosClientException e) {
            logger.error("COS客户端异常: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            logger.error("上传CIF文件时发生未知异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean deleteCIFFile(String formula) {
        if (formula == null || formula.trim().isEmpty()) {
            logger.error("化学式为空，无法删除文件");
            return false;
        }
        
        try {
            // 构建COS中的文件路径
            String objectKey = CIF_DIRECTORY + formula + ".cif";
            
            logger.info("开始删除COS中的CIF文件: {}", objectKey);
            
            // 获取COS客户端
            COSClient client = getCOSClient();
            
            // 检查文件是否存在
            if (!cifFileExists(formula)) {
                logger.warn("要删除的CIF文件不存在: {}", objectKey);
                return true; // 文件不存在也算删除成功
            }
            
            // 执行删除
            client.deleteObject(BUCKET_NAME, objectKey);
            
            logger.info("CIF文件删除成功: {}", objectKey);
            return true;
            
        } catch (CosServiceException e) {
            logger.error("COS服务异常，删除文件失败: {}", e.getMessage(), e);
            return false;
        } catch (CosClientException e) {
            logger.error("COS客户端异常，删除文件失败: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            logger.error("删除CIF文件时发生未知异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public String updateCIFFile(MultipartFile file, String oldFormula, String newFormula) {
        logger.info("开始更新CIF文件: {} -> {}", oldFormula, newFormula);
        
        // 如果化学式没有变化，直接覆盖上传
        if (oldFormula != null && oldFormula.equals(newFormula)) {
            return uploadCIFFile(file, newFormula);
        }
        
        // 化学式发生变化，需要删除旧文件并上传新文件
        boolean deleteSuccess = true;
        if (oldFormula != null && !oldFormula.trim().isEmpty()) {
            deleteSuccess = deleteCIFFile(oldFormula);
            if (!deleteSuccess) {
                logger.warn("删除旧CIF文件失败，但继续上传新文件: {}", oldFormula);
            }
        }
        
        // 上传新文件
        String newObjectKey = uploadCIFFile(file, newFormula);
        if (newObjectKey != null) {
            logger.info("CIF文件更新成功: {} -> {}", oldFormula, newFormula);
            return newObjectKey;
        } else {
            logger.error("上传新CIF文件失败: {}", newFormula);
            return null;
        }
    }
    
    @Override
    public boolean cifFileExists(String formula) {
        if (formula == null || formula.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 构建COS中的文件路径
            String objectKey = CIF_DIRECTORY + formula + ".cif";
            
            // 获取COS客户端
            COSClient client = getCOSClient();
            
            // 尝试获取文件元数据来检查文件是否存在
            client.getObjectMetadata(BUCKET_NAME, objectKey);
            return true;
            
        } catch (CosServiceException e) {
            // 如果是404错误，说明文件不存在
            if (e.getStatusCode() == 404) {
                return false;
            }
            logger.error("检查CIF文件存在性时发生COS服务异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("检查CIF文件存在性时发生异常: {}", e.getMessage());
            return false;
        }
    }
}
