package com.tcmp2.service;

import java.util.Map;

/**
 * 提示词模板服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025/6/23
 * @description : 提供提示词模板的读取和处理功能
 */
public interface PromptTemplateService {

    /**
     * 构建材料分析的AI提示词
     *
     * @param materialData 材料数据
     * @param cifContent CIF文件内容
     * @return 构建好的提示词
     */
    String buildAnalysisPrompt(Map<String, Object> materialData, String cifContent);

    /**
     * 从外部文件加载提示词模板
     *
     * @param templateName 模板名称
     * @return 模板内容
     */
    String loadTemplate(String templateName);
}
