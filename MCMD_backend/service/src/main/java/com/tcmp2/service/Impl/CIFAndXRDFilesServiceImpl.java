package com.tcmp2.service.Impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.GetObjectRequest;
import com.tcmp2.common.configs.TencentCOSConfig;
import com.tcmp2.service.CIFAndXRDFilesService;
import com.tcmp2.service.SearchService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> Zhang Chengwei
 * @Date: 2025/5/14 16:01
 * @Description:
 */
@Service
public class CIFAndXRDFilesServiceImpl implements CIFAndXRDFilesService {

    private static final Logger logger = LoggerFactory.getLogger(CIFAndXRDFilesServiceImpl.class);

    @Resource
    private TencentCOSConfig tencentCOSConfig;

    @Resource
    private SearchService searchService;
    
    // 新的内存处理Python脚本路径
    @Value("${python.script.memorygetxrd}")
    private String memoryPythonScriptPath;
    
    // COSClient的生命周期管理已移至TencentCOSConfig配置类
    
    /**
     * 获取COSClient实例 - 使用配置类的线程安全单例
     */
    private COSClient getCOSClient() {
        return tencentCOSConfig.getCOSClientInstance();
    }

    /**
     * @param materialId
     * @return
     */
    @Override
    public String getCIFFile(String materialId) throws IOException {
        // 1.先利用id获取化学式
        String formula = searchService.getFormulaById(materialId);
        if (formula == null) {
            throw new IOException("Material not found with ID: " + materialId);
        }
        String cosCIFPath = "CIF_files/"+ formula +".cif";

        // 获取COS客户端（单例）
        COSClient cosClient = getCOSClient();
        
        // 使用优化后的方法获取CIF文件内容
        return getCIFContentFromCOS(cosClient, cosCIFPath);
    }

    /**
     * 根据COS上的CIF文件，获取3个XRD文件
     * 完全优化版本 - 全内存处理，不使用本地文件
     *
     * @param materialId
     * @return
     */
    @Override
    public ResponseEntity<byte[]> getXRDFile(String materialId) {
        try {
            // 1.先利用id获取化学式
            String formula = searchService.getFormulaById(materialId);
            if (formula == null) {
                return ResponseEntity.notFound().build();
            }
            String cosCIFPath = "CIF_files/"+ formula +".cif";
            
            // 获取COS客户端（单例）
            COSClient cosClient = getCOSClient();
            
            // 直接从COS获取CIF文件内容，不下载到本地
            String cifContent = getCIFContentFromCOS(cosClient, cosCIFPath);
            
            if (cifContent != null && !cifContent.isEmpty()) {
                // 使用新的方法在内存中直接处理 - 无需任何本地文件
                List<XRDData> xrdDataList = generateXRDDataDirectlyInMemory(cifContent, formula);
                
                if (xrdDataList != null && !xrdDataList.isEmpty()) {
                    // 将XRD数据打包为ZIP文件（在内存中处理）
                    byte[] zipData = createZipDataInMemory(xrdDataList, formula);
                    
                    // 设置响应头
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                    headers.setContentDispositionFormData("attachment", formula + "_xrd.zip");
                    
                    return new ResponseEntity<>(zipData, headers, HttpStatus.OK);
                }
            }
            
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    

    
    /**
     * 直接从COS获取CIF文件内容，不下载到本地
     */
    private String getCIFContentFromCOS(COSClient cosClient, String objectKey) {
        try {
            // 创建获取文件请求
            GetObjectRequest getObjectRequest = new GetObjectRequest("mat-database-zcw-1311407817", objectKey);
            
            // 获取文件对象
            COSObject cosObject = cosClient.getObject(getObjectRequest);
            
            // 获取文件输入流
            InputStream inputStream = cosObject.getObjectContent();
            
            // 读取文件内容
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, bytesRead);
            }
            
            // 关闭流
            inputStream.close();

            // 使用UTF-8编码确保字符正确解析，避免中文乱码
            return result.toString(StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 表示XRD数据的内部类
     */
    private static class XRDData {
        private String filename;
        private String content;
        
        public XRDData(String filename, String content) {
            this.filename = filename;
            this.content = content;
        }
        
        public String getFilename() {
            return filename;
        }
        
        public String getContent() {
            return content;
        }
    }
    
    /**
     * 在内存中生成XRD数据，使用新的完全内存处理方式
     */
    private List<XRDData> generateXRDDataDirectlyInMemory(String cifContent, String formula) {
        try {
            logger.info("开始为材料 {} 生成XRD数据", formula);
            
            // 构建Python命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                "python3", // 使用python3命令
                memoryPythonScriptPath, // MemoryGetxrd.py脚本路径
                formula // 化学式/元素名称
            );
            
            // 设置环境变量（Docker容器中的路径）
            Map<String, String> env = processBuilder.environment();
            env.put("PYTHONPATH", "/app");
            
            // 不再合并错误流和标准输出流，分别处理
            processBuilder.redirectErrorStream(false);
            
            // 启动进程
            Process process = processBuilder.start();
            
            // 将CIF内容写入进程的标准输入，使用UTF-8编码
            logger.debug("向Python脚本写入CIF内容，长度: {}", cifContent.length());
            try (OutputStream stdin = process.getOutputStream()) {
                stdin.write(cifContent.getBytes(StandardCharsets.UTF_8));
                stdin.flush();
            }
            
            // 创建单独的线程读取错误输出，添加超时和异常处理
            StringBuilder errorOutput = new StringBuilder();
            Thread errorThread = new Thread(() -> {
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                        logger.warn("Python错误输出: {}", line);
                    }
                } catch (IOException e) {
                    logger.error("读取Python错误输出时发生异常: {}", e.getMessage(), e);
                }
            });
            errorThread.setName("Python-Error-Reader-" + System.currentTimeMillis());
            errorThread.setDaemon(true); // 设置为守护线程，避免阻止JVM退出
            errorThread.start();
            
            // 读取标准输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }
            }
            
            // 等待进程完成
            boolean completed = process.waitFor(120, TimeUnit.SECONDS);
            if (!completed) {
                process.destroy();
                logger.error("Python脚本执行超时");
                logger.error("错误日志: {}", errorOutput.toString());

                // 中断错误读取线程
                errorThread.interrupt();
                try {
                    errorThread.join(5000); // 最多等待5秒
                } catch (InterruptedException e) {
                    logger.warn("等待错误读取线程结束时被中断");
                    Thread.currentThread().interrupt();
                }
                return null;
            }

            // 确保错误输出线程完成，添加超时机制
            try {
                errorThread.join(10000); // 最多等待10秒
                if (errorThread.isAlive()) {
                    logger.warn("错误读取线程未能在10秒内结束，强制中断");
                    errorThread.interrupt();
                }
            } catch (InterruptedException e) {
                logger.warn("等待错误读取线程结束时被中断");
                Thread.currentThread().interrupt();
            }
            
            int exitCode = process.exitValue();
            if (exitCode != 0) {
                logger.error("Python脚本执行失败，退出码: {}", exitCode);
                logger.error("错误日志: {}", errorOutput.toString());
                return null;
            }
            
            // 解析JSON输出
            String jsonOutput = output.toString().trim();
            if (jsonOutput.isEmpty()) {
                logger.error("Python脚本没有输出任何内容");
                logger.error("错误日志: {}", errorOutput.toString());
                return null;
            }

            logger.info("解析Python脚本输出的JSON数据");
            List<XRDData> xrdDataList = new ArrayList<>();
            
            try {
                ObjectMapper mapper = new ObjectMapper();
                // 检查是否是错误信息
                if (jsonOutput.contains("\"error\":")) {
                    Map<String, String> errorMap = mapper.readValue(jsonOutput, 
                            new TypeReference<Map<String, String>>() {});
                    String errorMsg = errorMap.get("error");
                    String errorType = errorMap.get("type");
                    logger.error("Python脚本报告错误: {} - {}", errorType, errorMsg);
                    logger.error("错误日志: {}", errorOutput.toString());
                    return null;
                }
                
                List<Map<String, String>> resultList = mapper.readValue(jsonOutput, 
                        new TypeReference<List<Map<String, String>>>() {});

                logger.info("成功解析JSON数据，结果数量: {}", resultList.size());

                for (Map<String, String> result : resultList) {
                    String filename = result.get("filename");
                    String content = result.get("content");
                    logger.debug("解析结果: {}, 内容长度: {}", filename, content.length());
                    xrdDataList.add(new XRDData(filename, content));
                }
            } catch (Exception e) {
                logger.error("解析Python脚本输出失败: {}", e.getMessage());
                logger.error("JSON输出: {}", jsonOutput);
                logger.error("错误日志: {}", errorOutput.toString());
                logger.error("异常详情", e);
                return null;
            }

            logger.info("成功生成XRD数据，共 {} 个文件", xrdDataList.size());
            return xrdDataList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 在内存中创建ZIP数据
     */
    private byte[] createZipDataInMemory(List<XRDData> xrdDataList, String baseName) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        
        try {
            for (XRDData xrdData : xrdDataList) {
                ZipEntry entry = new ZipEntry(xrdData.getFilename());
                zos.putNextEntry(entry);

                // 使用UTF-8编码确保字符正确写入
                byte[] data = xrdData.getContent().getBytes(StandardCharsets.UTF_8);
                zos.write(data, 0, data.length);
                zos.closeEntry();
            }
        } finally {
            zos.close();
        }
        
        return baos.toByteArray();
    }
}
