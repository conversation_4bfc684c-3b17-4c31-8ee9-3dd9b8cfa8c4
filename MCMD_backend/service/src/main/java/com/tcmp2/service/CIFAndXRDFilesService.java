package com.tcmp2.service;

import org.springframework.http.ResponseEntity;

import java.io.IOException;

/**
 * <AUTHOR> <PERSON>
 * @Date: 2025/5/14 16:00
 * @Description:
 */
public interface CIFAndXRDFilesService {

    /**
     * 获取CIF文件
     *
     * @param materialId
     * @return
     */
    String getCIFFile(String materialId) throws IOException;

    /**
     * 根据COS上的CIF文件，获取3个XRD文件
     *
     * @param materialId
     * @return
     */
    ResponseEntity<byte[]> getXRDFile(String materialId);
}
