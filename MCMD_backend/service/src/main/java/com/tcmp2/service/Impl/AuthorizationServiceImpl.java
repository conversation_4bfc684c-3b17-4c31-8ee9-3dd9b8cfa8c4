package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.Material;
import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.AuthorizationService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * 权限验证服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 10:15
 * @description : 处理用户权限验证相关业务逻辑
 */
@Service
public class AuthorizationServiceImpl implements AuthorizationService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthorizationServiceImpl.class);
    private static final String USERS_COLLECTION = "users";
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Override
    public boolean canModifyMaterial(String username, Material material) {
        if (username == null || material == null) {
            return false;
        }

        // 获取用户角色
        UserRole userRole = UserRole.fromCode(getUserRole(username));

        // 管理员可以修改所有材料
        if (userRole.isAdmin()) {
            logger.debug("用户 {} 是管理员，允许修改材料 {}", username, material.getId());
            return true;
        }

        // 普通用户只能修改自己创建的材料
        boolean canModify = username.equals(material.getCreatedBy());
        logger.debug("用户 {} {} 修改材料 {} (创建者: {})",
                    username, canModify ? "可以" : "不能",
                    material.getId(), material.getCreatedBy());

        return canModify;
    }
    
    @Override
    public boolean canDeleteMaterial(String username, Material material) {
        if (username == null || material == null) {
            return false;
        }

        // 获取用户角色
        UserRole userRole = UserRole.fromCode(getUserRole(username));

        // 管理员可以删除所有材料
        if (userRole.isAdmin()) {
            return true;
        }

        // 普通用户只能删除自己创建的材料
        boolean canDelete = username.equals(material.getCreatedBy());

        return canDelete;
    }
    
    @Override
    public boolean isAdmin(String username) {
        if (username == null) {
            return false;
        }

        String role = getUserRole(username);
        boolean isAdminUser = UserRole.ADMIN.getCode().equals(role);

        return isAdminUser;
    }
    
    @Override
    public String getUserRole(String username) {
        if (username == null) {
            return UserRole.USER.getCode(); // 默认为普通用户角色
        }

        try {
            Query query = new Query(Criteria.where("username").is(username));
            User user = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (user != null && user.getRole() != null) {
                return user.getRole();
            }

            // 如果用户存在但没有角色字段，默认为普通用户
            return UserRole.USER.getCode();

        } catch (Exception e) {
            logger.error("获取用户 {} 角色时发生错误", username, e);
            return UserRole.USER.getCode();
        }
    }



    /**
     * 检查用户是否可以添加数据
     */
    public boolean canAddMaterial(String username) {
        if (username == null) {
            return false;
        }

        UserRole userRole = UserRole.fromCode(getUserRole(username));
        // 普通用户和管理员都可以添加数据
        return userRole.canModifyData();
    }

    /**
     * 检查用户状态是否有效
     */
    public boolean isUserActive(String username) {
        if (username == null) {
            return false;
        }

        try {
            Query query = new Query(Criteria.where("username").is(username));
            User user = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (user != null && user.getStatus() != null) {
                return "active".equals(user.getStatus());
            }

            return false;
        } catch (Exception e) {
            logger.error("检查用户 {} 状态时发生错误", username, e);
            return false;
        }
    }

}
