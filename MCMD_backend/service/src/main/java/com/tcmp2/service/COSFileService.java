package com.tcmp2.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-05-31 16:30
 * @description : 腾讯云COS文件管理服务接口
 */
public interface COSFileService {
    
    /**
     * 上传CIF文件到COS
     * 
     * @param file 上传的文件
     * @param formula 材料化学式，用作文件名
     * @return 上传成功返回COS中的文件路径，失败返回null
     */
    String uploadCIFFile(MultipartFile file, String formula);
    
    /**
     * 删除COS中的CIF文件
     * 
     * @param formula 材料化学式
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteCIFFile(String formula);
    
    /**
     * 更新COS中的CIF文件（先删除旧文件，再上传新文件）
     * 
     * @param file 新的CIF文件
     * @param oldFormula 旧的化学式（用于删除旧文件）
     * @param newFormula 新的化学式（用于上传新文件）
     * @return 更新成功返回新文件的COS路径，失败返回null
     */
    String updateCIFFile(MultipartFile file, String oldFormula, String newFormula);
    
    /**
     * 检查COS中是否存在指定的CIF文件
     * 
     * @param formula 材料化学式
     * @return 存在返回true，不存在返回false
     */
    boolean cifFileExists(String formula);
}
