package com.tcmp2.service.Impl;

import com.tcmp2.service.PromptTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 提示词模板服务实现
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025/6/23
 * @description : 从外部文件读取和处理提示词模板
 */
@Service
public class PromptTemplateServiceImpl implements PromptTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(PromptTemplateServiceImpl.class);

    @Override
    public String buildAnalysisPrompt(Map<String, Object> materialData, String cifContent) {
        try {
            // 加载外部提示词模板
            String template = loadTemplate("material-analysis-detailed-prompt.md");
            
            // 替换模板变量
            String prompt = replaceTemplateVariables(template, materialData, cifContent);
            
            return prompt;
            
        } catch (Exception e) {
            logger.error("构建分析提示词失败: {}", e.getMessage(), e);
            // 如果外部模板加载失败，返回错误信息
            return "提示词模板加载失败，请检查配置文件。错误: " + e.getMessage();
        }
    }

    @Override
    public String loadTemplate(String templateName) {
        try {
            ClassPathResource resource = new ClassPathResource("prompts/" + templateName);
            byte[] bytes = resource.getInputStream().readAllBytes();
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("加载提示词模板失败: {}", templateName, e);
            throw new RuntimeException("无法加载提示词模板: " + templateName, e);
        }
    }

    /**
     * 替换模板中的变量
     */
    private String replaceTemplateVariables(String template, Map<String, Object> materialData, String cifContent) {
        String result = template;
        
        // 替换材料基本信息
        result = result.replace("{materialId}", String.valueOf(materialData.get("id")));
        result = result.replace("{formula}", String.valueOf(materialData.get("Formula")));
        result = result.replace("{structure}", String.valueOf(materialData.get("Structure")));
        result = result.replace("{crystalSystem}", String.valueOf(materialData.get("Crystal_system")));
        result = result.replace("{spaceGroupNum}", String.valueOf(materialData.get("Space_group_num")));
        result = result.replace("{spaceGroupSym}", String.valueOf(materialData.get("Space_group_sym")));
        result = result.replace("{pointGroup}", String.valueOf(materialData.get("Point_group")));
        result = result.replace("{doi}", String.valueOf(materialData.get("DOI")));
        
        // 替换晶格参数
        result = result.replace("{cellA}", String.valueOf(materialData.get("a")));
        result = result.replace("{cellB}", String.valueOf(materialData.get("b")));
        result = result.replace("{cellC}", String.valueOf(materialData.get("c")));
        result = result.replace("{cellAlpha}", String.valueOf(materialData.get("a1")));
        result = result.replace("{cellBeta}", String.valueOf(materialData.get("b1")));
        result = result.replace("{cellGamma}", String.valueOf(materialData.get("c1")));
        result = result.replace("{cellVolume}", String.valueOf(materialData.get("V_unit_cell")));
        result = result.replace("{density}", String.valueOf(materialData.get("Density")));
        result = result.replace("{moleMass}", String.valueOf(materialData.get("Mole_Mass")));
        result = result.replace("{moleVolume}", String.valueOf(materialData.get("Mole_Volume")));
        
        // 替换电子性质
        result = result.replace("{bandGap}", String.valueOf(materialData.get("GGA_band_gap")));
        result = result.replace("{formationEnergy}", String.valueOf(materialData.get("E_hull")));
        
        // 替换磁性参数
        result = result.replace("{magneticLattice}", String.valueOf(materialData.get("magnetic_lattice")));
        result = result.replace("{magIon}", String.valueOf(materialData.get("Mag_ion")));
        result = result.replace("{magIonV}", String.valueOf(materialData.get("Mag_ion_V")));
        result = result.replace("{magIonM}", String.valueOf(materialData.get("Mag_ion_m")));
        result = result.replace("{magneticMoment}", String.valueOf(materialData.get("Magnetic_moment")));
        result = result.replace("{effectiveSpin}", String.valueOf(materialData.get("Effective_Spin")));
        result = result.replace("{neelTemperature}", String.valueOf(materialData.get("T_N")));
        result = result.replace("{accessTemperature}", String.valueOf(materialData.get("T_access")));
        
        // 替换磁性相互作用参数
        result = result.replace("{sgsVol}", String.valueOf(materialData.get("S_GS_Vol")));
        result = result.replace("{sgsMass}", String.valueOf(materialData.get("S_GS_Mass")));
        result = result.replace("{sgsMol}", String.valueOf(materialData.get("S_GS_mol")));
        result = result.replace("{sgsMol2}", String.valueOf(materialData.get("S_GS_mol2")));
        result = result.replace("{nmRln2J1}", String.valueOf(materialData.get("Nm_Rln_2J_1_")));
        
        // 替换磁性离子距离
        result = result.replace("{nearestMagIonDistanceInPlane}", String.valueOf(materialData.get("Nearest_Mag_Ion_Distance_In_plane")));
        result = result.replace("{nearestMagIonDistanceInterPlane}", String.valueOf(materialData.get("Nearest_Mag_Ion_Distance_inter_plane")));

        // 处理自定义属性
        String customPropertiesSection = formatCustomPropertiesForPrompt(materialData);
        result = result.replace("{customPropertiesSection}", customPropertiesSection);

        // 处理CIF文件内容部分 - 仅负责格式化，不负责获取
        String cifSection = formatCifContentForPrompt(cifContent);
        result = result.replace("{cifContentSection}", cifSection);

        return result;
    }

    /**
     * 格式化CIF内容用于提示词
     * 仅负责格式化显示，不负责获取CIF文件
     */
    private String formatCifContentForPrompt(String cifContent) {
        if (cifContent == null || cifContent.equals("CIF文件不可用") || cifContent.startsWith("CIF文件获取失败")) {
            return ""; // 如果CIF不可用，返回空字符串，模板中不显示CIF部分
        }

        StringBuilder cifSection = new StringBuilder();
        cifSection.append("\n## CIF文件信息\n");
        cifSection.append("```\n");

        // 截取CIF内容，避免提示词过长
        int maxLength = 1000;
        if (cifContent.length() <= maxLength) {
            cifSection.append(cifContent);
        } else {
            cifSection.append(cifContent.substring(0, maxLength));
        }

        cifSection.append("\n```\n");

        if (cifContent.length() > maxLength) {
            cifSection.append("（CIF文件内容较长，已截取前").append(maxLength).append("字符）\n");
        }

        return cifSection.toString();
    }

    /**
     * 格式化自定义属性用于提示词
     * 将自定义属性转换为可读的格式
     */
    @SuppressWarnings("unchecked")
    private String formatCustomPropertiesForPrompt(Map<String, Object> materialData) {
        Object customPropsObj = materialData.get("customProperties");

        if (customPropsObj == null) {
            return ""; // 如果没有自定义属性，返回空字符串
        }

        if (!(customPropsObj instanceof Map)) {
            return ""; // 如果自定义属性不是Map类型，返回空字符串
        }

        Map<String, Object> customProperties = (Map<String, Object>) customPropsObj;

        if (customProperties.isEmpty()) {
            return ""; // 如果自定义属性为空，返回空字符串
        }

        StringBuilder customSection = new StringBuilder();
        customSection.append("\n## 自定义属性\n");

        // 遍历自定义属性并格式化
        customProperties.forEach((key, value) -> {
            if (value != null) {
                // 格式化属性名（将下划线替换为空格，首字母大写）
                String formattedKey = formatPropertyName(key);
                String formattedValue = formatPropertyValue(value);
                customSection.append("- **").append(formattedKey).append("**: ").append(formattedValue).append("\n");
            }
        });

        return customSection.toString();
    }

    /**
     * 格式化属性名
     */
    private String formatPropertyName(String key) {
        if (key == null || key.isEmpty()) {
            return key;
        }

        // 将下划线替换为空格，并进行首字母大写处理
        String[] words = key.replace("_", " ").split(" ");
        StringBuilder formatted = new StringBuilder();

        for (String word : words) {
            if (!word.isEmpty()) {
                if (formatted.length() > 0) {
                    formatted.append(" ");
                }
                // 首字母大写，其余保持原样
                formatted.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    formatted.append(word.substring(1));
                }
            }
        }

        return formatted.toString();
    }

    /**
     * 格式化属性值
     */
    private String formatPropertyValue(Object value) {
        if (value == null) {
            return "N/A";
        }

        if (value instanceof Number) {
            // 对于数字，保留合理的小数位数
            if (value instanceof Double || value instanceof Float) {
                double doubleValue = ((Number) value).doubleValue();
                // 如果是整数，不显示小数点
                if (doubleValue == Math.floor(doubleValue)) {
                    return String.valueOf((long) doubleValue);
                } else {
                    // 保留最多6位小数，去除尾随零
                    return String.format("%.6f", doubleValue).replaceAll("0*$", "").replaceAll("\\.$", "");
                }
            } else {
                return value.toString();
            }
        }

        if (value instanceof Boolean) {
            return ((Boolean) value) ? "是" : "否";
        }

        // 对于字符串和其他类型，直接转换
        String stringValue = value.toString();

        // 如果字符串过长，进行截取
        if (stringValue.length() > 100) {
            return stringValue.substring(0, 97) + "...";
        }

        return stringValue;
    }
}
