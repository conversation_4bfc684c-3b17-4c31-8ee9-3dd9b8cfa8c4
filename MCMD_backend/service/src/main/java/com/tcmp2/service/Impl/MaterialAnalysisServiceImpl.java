package com.tcmp2.service.Impl;

import com.tcmp2.service.CIFAndXRDFilesService;
import com.tcmp2.service.MaterialAnalysisService;
import com.tcmp2.service.PromptTemplateService;
import com.tcmp2.utils.MarkdownStreamProcessor;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import dev.langchain4j.service.AiServices;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 材料AI分析服务实现
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025/5/31
 * @description : 使用LangChain4J实现材料数据的AI分析
 */
@Service
public class MaterialAnalysisServiceImpl implements MaterialAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialAnalysisServiceImpl.class);

    @Value("${langchain4j.open-ai.streaming-chat-model.api-key}")
    private String apiKey;

    @Value("${langchain4j.open-ai.chat-model.base-url}")
    private String baseUrl;

    @Value("${langchain4j.open-ai.chat-model.model-name}")
    private String modelName;

    @Resource
    private CIFAndXRDFilesService cifAndXRDFilesService;

    @Resource
    private PromptTemplateService promptTemplateService;

    // AI助手接口 - 支持Function Calling
    interface MaterialAnalysisAssistant {
        Flux<String> analyze(String prompt);
    }

    @Override
    public Flux<String> analyzeMaterial(Map<String, Object> materialData, String cifContent) {
        try {
            // 使用外部提示词模板服务构建分析提示词
            String prompt = promptTemplateService.buildAnalysisPrompt(materialData, cifContent);

            // 创建流式聊天模型
            StreamingChatModel model = OpenAiStreamingChatModel.builder()
                    .apiKey(apiKey)
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .temperature(0.7)
                    .build();

            // 创建AI服务代理（暂时不使用Function Calling，但保留CIFFileTools结构）
            MaterialAnalysisAssistant assistant = AiServices.create(MaterialAnalysisAssistant.class, model);

            // 创建Markdown流处理器
            MarkdownStreamProcessor markdownProcessor = new MarkdownStreamProcessor();

            // 调用AI分析并返回流式响应，使用Markdown处理器
            Flux<String> rawStream = assistant.analyze(prompt)
                    .onErrorResume(e -> {
                        logger.error("AI分析过程中发生错误: {}", e.getMessage(), e);
                        return Flux.just("[ERROR] AI分析失败: " + e.getMessage());
                    });

            // 使用Markdown处理器处理流式输出
            return markdownProcessor.processMarkdownStream(rawStream)
                    .doFinally(signalType -> {
                        // 清理ThreadLocal资源，防止内存泄漏
                        markdownProcessor.cleanup();
                        logger.debug("AI分析完成，已清理Markdown处理器资源");
                    });

        } catch (Exception e) {
            logger.error("启动AI分析时发生错误: {}", e.getMessage(), e);
            return Flux.just("[ERROR] 启动AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public String getCifContent(String materialId) {
        try {
            // 职责：仅负责获取原始CIF文件内容，不做任何格式化处理
            // 格式化工作由PromptTemplateService负责
            // 直接使用CIFAndXRDFilesService，无需中间层
            String cifContent = cifAndXRDFilesService.getCIFFile(materialId);
            logger.debug("成功获取CIF文件内容，materialId: {}, 内容长度: {} 字符",
                        materialId, cifContent != null ? cifContent.length() : 0);
            return cifContent;

        } catch (Exception e) {
            logger.error("通过CIFAndXRDFilesService获取CIF文件失败: {}", e.getMessage(), e);
            return "CIF文件获取失败: " + e.getMessage();
        }
    }
}
