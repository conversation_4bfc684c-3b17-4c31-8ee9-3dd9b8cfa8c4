package com.tcmp2.service;

import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 23:58
 * @description : 材料数据CRUD服务接口
 */
public interface DataCURDService {
    /**
     * 根据ID获取材料
     *
     * @param materialId 材料ID
     * @return 材料数据
     */
    Map<String, Object> getMaterialById(String materialId);

    /**
     * 添加材料
     *
     * @param data 材料数据
     * @param currentUser 当前用户名
     * @return 添加结果
     */
    Map<String, Object> addMaterial(Map<String, Object> data, String currentUser);

    /**
     * 添加材料（带CIF文件）
     *
     * @param data 材料数据
     * @param cifFile CIF文件
     * @param currentUser 当前用户名
     * @return 添加结果
     */
    Map<String, Object> addMaterialWithCIF(Map<String, Object> data, MultipartFile cifFile, String currentUser);

    /**
     * 更新材料
     *
     * @param materialId 材料ID
     * @param data      更新数据
     * @param currentUser 当前用户名
     * @return 更新结果
     */
    Map<String, Object> updateMaterial(String materialId, Map<String, Object> data, String currentUser);

    /**
     * 更新材料（带CIF文件）
     *
     * @param materialId 材料ID
     * @param data      更新数据
     * @param cifFile   新的CIF文件（可选）
     * @param currentUser 当前用户名
     * @return 更新结果
     */
    Map<String, Object> updateMaterialWithCIF(String materialId, Map<String, Object> data, MultipartFile cifFile, String currentUser);

    /**
     * 删除材料
     *
     * @param materialId 材料ID
     * @param currentUser 当前用户名
     * @return 删除结果
     */
    Map<String, Object> deleteMaterial(String materialId, String currentUser);

    /**
     * 删除所有材料（仅管理员）
     *
     * @param currentUser 当前用户名
     * @return 删除结果
     */
    Map<String, Object> deleteAllMaterials(String currentUser);

    /**
     * ZIP批量上传材料
     *
     * @param zipFile ZIP文件
     * @param currentUser 当前用户名
     * @return 批量上传结果
     */
    Map<String, Object> batchUploadFromZip(MultipartFile zipFile, String currentUser);
}
