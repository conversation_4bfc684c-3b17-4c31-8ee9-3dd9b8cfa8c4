package com.tcmp2.service;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;

import java.util.List;
import java.util.Map;

/**
 * 用户管理服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 10:00
 * @description : 用户注册、管理相关服务
 */
public interface UserService {
    

    
    /**
     * 管理员创建用户
     *
     * @param currentUser 当前用户（管理员）
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @param realName 真实姓名
     * @param organization 机构/组织
     * @param role 用户角色
     * @return 创建结果
     */
    Map<String, String> createUserByAdmin(String currentUser, String username, String password, String email, String realName, String organization, String role);

    /**
     * 获取所有用户列表（仅管理员可用）
     *
     * @param currentUser 当前用户
     * @return 用户列表
     */
    List<User> getAllUsers(String currentUser);
    
    /**
     * 更新用户信息（仅管理员可用）
     *
     * @param currentUser 当前用户
     * @param targetUsername 目标用户名
     * @param email 邮箱
     * @param realName 真实姓名
     * @param organization 组织机构
     * @return 操作结果
     */
    Map<String, String> updateUserInfo(String currentUser, String targetUsername, String email, String realName, String organization);

    /**
     * 更新用户角色（仅管理员可用）
     *
     * @param currentUser 当前用户
     * @param targetUsername 目标用户名
     * @param newRole 新角色
     * @return 操作结果
     */
    Map<String, String> updateUserRole(String currentUser, String targetUsername, UserRole newRole);
    
    /**
     * 更新用户状态（仅管理员可用）
     *
     * @param currentUser 当前用户
     * @param targetUsername 目标用户名
     * @param status 新状态 (active, inactive, banned)
     * @return 操作结果
     */
    Map<String, String> updateUserStatus(String currentUser, String targetUsername, String status);
    
    /**
     * 获取用户详细信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    User getUserInfo(String username);
    
    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean isEmailExists(String email);

    /**
     * 修改用户密码
     *
     * @param username 用户名
     * @param currentPassword 当前密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    Map<String, String> changePassword(String username, String currentPassword, String newPassword);

    /**
     * 重置用户密码（仅管理员可用）
     *
     * @param currentUser 当前用户（管理员）
     * @param targetUsername 目标用户名
     * @return 操作结果，包含新密码
     */
    Map<String, String> resetUserPassword(String currentUser, String targetUsername);
}
