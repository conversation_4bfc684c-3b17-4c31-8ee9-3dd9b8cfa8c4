package com.tcmp2.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;

import java.util.regex.Pattern;

/**
 * Markdown流式处理器 - 全面版本
 * 参考Dify的实现，确保Markdown语法元素不被破坏
 * 核心思路：实时应用格式修复，通过增量输出实现流式效果
 */
public class MarkdownStreamProcessor {

    private static final Logger logger = LoggerFactory.getLogger(MarkdownStreamProcessor.class);

    // 状态管理 - 使用ThreadLocal确保线程安全
    private final ThreadLocal<String> lastProcessedChunk = ThreadLocal.withInitial(() -> "");
    private final ThreadLocal<Boolean> inCodeBlock = ThreadLocal.withInitial(() -> false);
    private final ThreadLocal<Integer> codeBlockCount = ThreadLocal.withInitial(() -> 0);

    // 预编译的正则表达式模式 - 全面覆盖各种Markdown格式

    // 标题相关
    private static final Pattern HEADING_PATTERN = Pattern.compile("(^|\n)(#{1,6})([^\\s#])");
    private static final Pattern NUMBERED_HEADING_PATTERN = Pattern.compile("(^|\n)([0-9]+\\. [\\u4e00-\\u9fa5\\w]+)");

    // 列表相关
    private static final Pattern UNORDERED_LIST_PATTERN = Pattern.compile("(^|\n)([\\s]*[-*+])([^\\s])");
    private static final Pattern ORDERED_LIST_PATTERN = Pattern.compile("(^|\n)([\\s]*[0-9]+\\.)([^\\s])");

    // 文本格式相关
    private static final Pattern BOLD_PATTERN = Pattern.compile("\\*\\*([^*]+)\\*\\*");
    private static final Pattern ITALIC_PATTERN = Pattern.compile("(?<!\\*)\\*([^*]+)\\*(?!\\*)");
    private static final Pattern CODE_INLINE_PATTERN = Pattern.compile("`([^`]+)`");
    private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile("```");

    // 中文特殊处理
    private static final Pattern COLON_PATTERN = Pattern.compile("([\\u4e00-\\u9fa5\\w]+：)([\\u4e00-\\u9fa5\\w])");
    private static final Pattern CHINESE_PUNCTUATION_PATTERN = Pattern.compile("([\\u4e00-\\u9fa5])([，。！？；：])([\\u4e00-\\u9fa5])");

    // 链接和图片
    private static final Pattern LINK_PATTERN = Pattern.compile("\\[([^\\]]+)\\]\\(([^)]+)\\)");
    private static final Pattern IMAGE_PATTERN = Pattern.compile("!\\[([^\\]]*)\\]\\(([^)]+)\\)");

    // 表格
    private static final Pattern TABLE_ROW_PATTERN = Pattern.compile("(^|\n)(\\|[^|\n]+\\|)");

    // 引用
    private static final Pattern BLOCKQUOTE_PATTERN = Pattern.compile("(^|\n)>([^>\n])");

    // 分隔线 - 修复正则表达式，支持更多情况
    private static final Pattern HORIZONTAL_RULE_PATTERN = Pattern.compile("(^|\n)\\s*([-*_]{3,})\\s*(\n|$)");

    /**
     * 处理流式Markdown文本 - 简化版本，直接输出处理后的内容
     *
     * @param inputFlux 原始AI输出流
     * @return 处理后的Markdown流
     */
    public Flux<String> processMarkdownStream(Flux<String> inputFlux) {
        // 重置状态
        reset();

        return inputFlux
            .scan("", (accumulated, newChunk) -> {
                // 累积原始内容
                String fullContent = accumulated + newChunk;
                return fullContent;
            })
            .skip(1) // 跳过第一个空字符串
            .map(fullRawContent -> {
                // 应用Markdown格式修复到完整内容
                String processedContent = applyMarkdownFixes(fullRawContent);

                // 简化：直接返回处理后的完整内容，让前端处理增量显示
                return processedContent;
            })
            .distinctUntilChanged() // 只有内容变化时才发送
            .filter(content -> !content.isEmpty());
    }

    /**
     * 应用Markdown格式修复 - 全面版本
     * 参考Dify的实现，确保Markdown语法元素不被破坏
     */
    private String applyMarkdownFixes(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 检测代码块状态
        updateCodeBlockState(content);

        // 在代码块内，不进行格式处理
        if (inCodeBlock.get()) {
            return content;
        }

        // 1. 修复数字标题格式：1. 结构特征分析 -> ### 1. 结构特征分析
        content = NUMBERED_HEADING_PATTERN.matcher(content).replaceAll("$1### $2");

        // 2. 修复标题格式：###数字 -> ### 数字
        content = HEADING_PATTERN.matcher(content).replaceAll("$1$2 $3");

        // 3. 修复无序列表格式：-内容 -> - 内容，*内容 -> * 内容，+内容 -> + 内容
        content = UNORDERED_LIST_PATTERN.matcher(content).replaceAll("$1$2 $3");

        // 4. 修复有序列表格式：1.内容 -> 1. 内容
        content = ORDERED_LIST_PATTERN.matcher(content).replaceAll("$1$2 $3");

        // 5. 修复引用格式：>内容 -> > 内容
        content = BLOCKQUOTE_PATTERN.matcher(content).replaceAll("$1> $2");

        // 6. 修复表格格式：确保表格行格式正确
        content = TABLE_ROW_PATTERN.matcher(content).replaceAll("$1$2");

        // 7. 修复冒号后内容：词语：内容 -> 词语： 内容
        content = COLON_PATTERN.matcher(content).replaceAll("$1 $2");

        // 8. 处理中文标点符号间距
        content = CHINESE_PUNCTUATION_PATTERN.matcher(content).replaceAll("$1$2$3");

        // 9. 处理"注："的独立性 - 修复格式问题
        // 临时注释掉，避免与清理逻辑冲突
        // content = content.replaceAll("([^。！？\n])注：", "$1\n\n**注：**");
        // content = content.replaceAll("^注：", "**注：**");
        // content = content.replaceAll("\\*\\*注：\\*\\*\\s*\\*\\*\\*\\*", "**注：**"); // 修复重复星号

        // 智能清理不完整的内容
        // 1. 清理结尾的单独标签（注：、备注：、说明：等）
        content = content.replaceAll("\\*\\*(注|备注|说明|提示)：\\*\\*\\s*$", "");
        content = content.replaceAll("\n\n\\*\\*(注|备注|说明|提示)：\\*\\*\\s*\n*$", "");
        content = content.replaceAll("^\\*\\*(注|备注|说明|提示)：\\*\\*\\s*$", "");
        content = content.replaceAll("(注|备注|说明|提示)：\\s*$", "");

        // 2. 清理重复的标签行
        // 临时注释掉，可能导致问题
        // content = content.replaceAll("(?m)^(.+：)\\s*\n\\s*\\1\\s*$", "$1");

        // 3. 清理结尾的不完整句子（以冒号结尾但没有内容的行）
        content = content.replaceAll("\n\\s*[^：\n]*：\\s*$", "");

        // 10. 修复标题后直接跟列表项的问题
        content = content.replaceAll("(### .+?)([^:\n。！？]*)-\\s*(\\*\\*)", "$1$2\n\n- $3");
        content = content.replaceAll("(### .+?)([^:\n。！？]*)-\\s*([\\u4e00-\\u9fa5])", "$1$2\n\n- $3");

        // 11. 处理分隔线 - 确保分隔线格式正确
        content = HORIZONTAL_RULE_PATTERN.matcher(content).replaceAll("$1---$3");

        // 12. 确保列表项前后有适当的空行
        content = content.replaceAll("([^\n])\n(\\s*[-*+]\\s)", "$1\n\n$2");
        content = content.replaceAll("([^\n])\n(\\s*[0-9]+\\.\\s)", "$1\n\n$2");

        // 13. 确保标题前后有适当的空行
        content = content.replaceAll("([^\n])\n(#{1,6}\\s)", "$1\n\n$2");
        content = content.replaceAll("(#{1,6}\\s[^\n]+)\n([^\n#])", "$1\n\n$2");

        // 14. 清理开头和结尾的省略号和反引号 - 加强版
        // 首先清理开头的反引号（最重要）
        content = content.replaceAll("^`+\\s*", "");
        content = content.replaceAll("^\\s*`+\\s*", "");

        // 清理开头的省略号和反引号组合
        content = content.replaceAll("^[\\s]*[·…\\.`]{1,}[\\s]*", "");

        // 清理结尾的省略号和反引号
        content = content.replaceAll("[\\s]*[·…\\.`]{1,}[\\s]*$", "");

        // 清理行中间的单独省略号和反引号
        content = content.replaceAll("\n[\\s]*[·…\\.`]{1,}[\\s]*\n", "\n\n");
        content = content.replaceAll("\n[\\s]*[·…\\.`]{1,}[\\s]*$", "");

        // 清理单独一行的省略号和反引号
        content = content.replaceAll("^[·…\\.`]+$", "");
        content = content.replaceAll("\n[·…\\.`]+\n", "\n");
        content = content.replaceAll("\n[·…\\.`]+$", "");

        // 再次清理可能遗漏的开头反引号
        content = content.replaceAll("^`", "");
        content = content.replaceAll("\n`([^`])", "\n$1");

        // 15. 清理列表中间的空行（重点修复研究建议部分的问题）
        content = fixListEmptyLines(content);

        // 16. 清理多余空行
        content = content.replaceAll("\n{3,}", "\n\n");

        // 17. 确保文档结构完整性
        content = ensureDocumentStructure(content);

        return content;
    }

    /**
     * 检测代码块状态
     */
    private void updateCodeBlockState(String content) {
        long newCodeBlockCount = CODE_BLOCK_PATTERN.matcher(content).results().count();
        if (newCodeBlockCount != codeBlockCount.get()) {
            codeBlockCount.set((int) newCodeBlockCount);
            inCodeBlock.set((codeBlockCount.get() % 2 == 1));
        }
    }

    /**
     * 确保文档结构完整性
     */
    private String ensureDocumentStructure(String content) {
        // 确保文档以换行结尾
        if (!content.endsWith("\n")) {
            content += "\n";
        }

        // 确保标题层级正确
        content = content.replaceAll("(^|\n)(#{7,})", "$1######");

        // 确保列表嵌套正确
        content = fixListNesting(content);

        return content;
    }

    /**
     * 修复列表中间的空行问题（重点解决研究建议部分的格式问题）
     */
    private String fixListEmptyLines(String content) {
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inList = false;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            String trimmedLine = line.trim();

            // 检测列表项开始
            if (trimmedLine.matches("^[-*+]\\s.*") || trimmedLine.matches("^[0-9]+\\.\\s.*")) {
                inList = true;
                result.append(line);
                if (i < lines.length - 1) {
                    result.append("\n");
                }
                continue;
            }

            // 在列表中处理空行
            if (inList && trimmedLine.isEmpty()) {
                // 检查接下来是否还有列表项
                boolean hasMoreListItems = false;
                for (int j = i + 1; j < lines.length; j++) {
                    String nextLine = lines[j].trim();
                    if (nextLine.isEmpty()) continue; // 跳过连续空行
                    if (nextLine.matches("^[-*+]\\s.*") || nextLine.matches("^[0-9]+\\.\\s.*")) {
                        hasMoreListItems = true;
                        break;
                    } else {
                        // 遇到非列表项，列表结束
                        break;
                    }
                }

                if (hasMoreListItems) {
                    // 列表中间的空行，直接跳过不添加
                    continue;
                } else {
                    // 列表结束，保留空行并标记列表结束
                    inList = false;
                    result.append(line);
                    if (i < lines.length - 1) {
                        result.append("\n");
                    }
                }
                continue;
            }

            // 非列表项或列表项内容
            if (!trimmedLine.matches("^[-*+]\\s.*") && !trimmedLine.matches("^[0-9]+\\.\\s.*") && !trimmedLine.isEmpty()) {
                inList = false;
            }

            result.append(line);
            if (i < lines.length - 1) {
                result.append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 修复列表嵌套问题
     */
    private String fixListNesting(String content) {
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];

            // 检测列表项
            if (line.matches("^\\s*[-*+]\\s.*") || line.matches("^\\s*[0-9]+\\.\\s.*")) {
                // 确保列表项有正确的缩进
                int indent = 0;
                for (char c : line.toCharArray()) {
                    if (c == ' ' || c == '\t') {
                        indent++;
                    } else {
                        break;
                    }
                }

                // 标准化缩进（每级2个空格）
                int level = indent / 2;
                String standardIndent = "  ".repeat(level);
                line = line.replaceFirst("^\\s*", standardIndent);
            }

            result.append(line);
            if (i < lines.length - 1) {
                result.append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 重置处理器状态
     */
    public void reset() {
        lastProcessedChunk.set("");
        inCodeBlock.set(false);
        codeBlockCount.set(0);
    }

    /**
     * 清理ThreadLocal资源，防止内存泄漏
     */
    public void cleanup() {
        lastProcessedChunk.remove();
        inCodeBlock.remove();
        codeBlockCount.remove();
    }
}
