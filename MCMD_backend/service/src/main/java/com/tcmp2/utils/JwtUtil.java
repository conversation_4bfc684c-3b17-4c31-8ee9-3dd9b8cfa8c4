package com.tcmp2.utils;

import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.LoginService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * JWT工具类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 11:00
 * @description : 提供JWT令牌解析和用户信息提取功能
 */
@Component
public class JwtUtil {

    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);

    @Resource
    private LoginService loginService;

    @Resource
    private AuthorizationService authorizationService;

    /**
     * 从请求中提取用户名
     *
     * @param request HTTP请求
     * @return 用户名，如果提取失败返回null
     */
    public String extractUsernameFromRequest(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        if (token != null) {
            return loginService.validateToken(token);
        }
        return null;
    }

    /**
     * 从请求中提取用户角色
     *
     * @param request HTTP请求
     * @return 用户角色，如果提取失败返回null
     */
    public String extractRoleFromRequest(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        if (token != null) {
            String username = loginService.validateToken(token);
            if (username != null) {
                return authorizationService.getUserRole(username);
            }
        }
        return null;
    }
    
    /**
     * 从请求中提取JWT令牌
     *
     * @param request HTTP请求
     * @return JWT令牌，如果没有找到返回null
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        logger.debug("开始提取JWT令牌...");

        // 首先尝试从Authorization头中获取
        String authHeader = request.getHeader("Authorization");
        logger.debug("Authorization头: {}", authHeader);
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            logger.debug("从Authorization头中获取到token: {}...", token.substring(0, Math.min(20, token.length())));
            return token;
        }

        // 然后尝试从Cookie中获取
        logger.debug("检查Cookie...");
        if (request.getCookies() != null) {
            logger.debug("找到 {} 个Cookie", request.getCookies().length);
            for (var cookie : request.getCookies()) {
                logger.debug("Cookie: {} = {}", cookie.getName(), cookie.getValue());
                if ("jwt_token".equals(cookie.getName())) {
                    String token = cookie.getValue();
                    logger.debug("从Cookie中获取到jwt_token: {}...", token.substring(0, Math.min(20, token.length())));
                    return token;
                }
            }
        } else {
            logger.debug("请求中没有Cookie");
        }

        // 尝试从自定义Header中获取（用于EventSource等特殊情况）
        String lastEventIdHeader = request.getHeader("Last-Event-ID");
        if (lastEventIdHeader != null && lastEventIdHeader.startsWith("Bearer ")) {
            logger.debug("从Last-Event-ID Header中获取token");
            return lastEventIdHeader.substring(7);
        }

        // 临时兼容：从URL参数中获取（仅用于EventSource等特殊情况，存在安全风险）
        // 只在特定路径下允许URL参数传递token，并记录警告
        String requestPath = request.getRequestURI();
        if (isEventSourcePath(requestPath)) {
            String tokenParam = request.getParameter("token");
            if (tokenParam != null && !tokenParam.trim().isEmpty()) {
                logger.info("从URL参数中获取token，路径: {} - 跨域SSE连接使用URL参数认证", requestPath);
                return tokenParam;
            }
        }

        logger.debug("未找到任何JWT令牌");
        return null;
    }

    /**
     * 判断是否为EventSource路径（允许URL参数传递token的特殊路径）
     * @param requestPath 请求路径
     * @return 是否为EventSource路径
     */
    private boolean isEventSourcePath(String requestPath) {
        // 只有特定的EventSource端点才允许URL参数传递token
        return requestPath != null &&
               (requestPath.contains("/analyze") ||
                requestPath.contains("/stream"));
    }

    /**
     * 生成短期有效的令牌（用于SSE连接等特殊场景）
     * 简化实现：返回一个临时标识符，实际使用原始令牌
     * @param username 用户名
     * @param materialId 材料ID（可选，用于限制令牌使用范围）
     * @return 短期令牌标识
     */
    public String generateShortTermToken(String username, String materialId) {
        try {
            // 简化实现：生成一个临时标识符
            // 实际场景中，前端仍然使用原始JWT令牌
            long timestamp = System.currentTimeMillis();
            return "sse_" + username + "_" + materialId + "_" + timestamp;
        } catch (Exception e) {
            logger.error("生成短期令牌失败", e);
            throw new RuntimeException("生成令牌失败", e);
        }
    }

    /**
     * 验证短期令牌（简化版本）
     * @param token 令牌
     * @param materialId 材料ID
     * @return 用户名，如果验证失败返回null
     */
    public String validateShortTermToken(String token, String materialId) {
        // 简化实现：直接使用标准令牌验证
        return loginService.validateToken(token);
    }

}
