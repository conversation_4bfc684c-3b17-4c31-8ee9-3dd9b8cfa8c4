// MongoDB索引创建脚本
// 运行方式: mongo ************************************************************ create-indexes.js

use MCMD;

// 为materials集合创建索引
db.materials.createIndex({ "id": 1 }, { unique: true, name: "idx_id_unique" });
db.materials.createIndex({ "Formula": 1 }, { name: "idx_formula" });
db.materials.createIndex({ "Space_group_sym": 1 }, { name: "idx_space_group_sym" });
db.materials.createIndex({ "Space_group_num": 1 }, { name: "idx_space_group_num" });
db.materials.createIndex({ "magnetic_lattice": 1 }, { name: "idx_magnetic_lattice" });
db.materials.createIndex({ "Structure": 1 }, { name: "idx_structure" });
db.materials.createIndex({ "createdBy": 1 }, { name: "idx_created_by" });
db.materials.createIndex({ "createdAt": -1 }, { name: "idx_created_at_desc" });
db.materials.createIndex({ "lastModifiedAt": -1 }, { name: "idx_last_modified_at_desc" });

// 复合索引用于常见查询组合
db.materials.createIndex({ "createdBy": 1, "createdAt": -1 }, { name: "idx_created_by_date" });
db.materials.createIndex({ "Space_group_num": 1, "magnetic_lattice": 1 }, { name: "idx_space_group_magnetic" });

// 为users集合创建索引
db.users.createIndex({ "username": 1 }, { unique: true, name: "idx_username_unique" });
db.users.createIndex({ "role": 1 }, { name: "idx_role" });
db.users.createIndex({ "status": 1 }, { name: "idx_status" });

print("索引创建完成！");
print("当前materials集合索引:");
db.materials.getIndexes().forEach(function(index) {
    print("- " + index.name + ": " + JSON.stringify(index.key));
});

print("\n当前users集合索引:");
db.users.getIndexes().forEach(function(index) {
    print("- " + index.name + ": " + JSON.stringify(index.key));
});
