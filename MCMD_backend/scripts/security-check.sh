#!/bin/bash

# MCMD 安全检查脚本
# 在部署前运行此脚本检查安全配置

echo "🔒 MCMD 安全配置检查开始..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果计数
PASS=0
FAIL=0
WARN=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✓${NC} $1"
    ((PASS++))
}

check_fail() {
    echo -e "${RED}✗${NC} $1"
    ((FAIL++))
}

check_warn() {
    echo -e "${YELLOW}⚠${NC} $1"
    ((WARN++))
}

echo "📋 检查配置文件..."

# 检查环境变量文件
if [ -f ".env" ]; then
    check_pass "环境变量文件 .env 存在"
    
    # 检查JWT密钥
    if grep -q "JWT_SECRET=" .env; then
        JWT_SECRET=$(grep "JWT_SECRET=" .env | cut -d'=' -f2)
        if [ ${#JWT_SECRET} -ge 32 ]; then
            check_pass "JWT密钥长度符合要求 (${#JWT_SECRET} 字符)"
        else
            check_fail "JWT密钥长度不足 (${#JWT_SECRET} 字符，建议至少32字符)"
        fi
        
        # 检查是否使用默认密钥
        if [[ "$JWT_SECRET" == *"MCMD_JWT_SECRET_KEY"* ]]; then
            check_fail "检测到默认JWT密钥，请更换为随机生成的密钥"
        else
            check_pass "JWT密钥已自定义"
        fi
    else
        check_fail "未找到JWT_SECRET配置"
    fi
    
    # 检查数据库配置
    if grep -q "MONGODB_URI=" .env; then
        MONGODB_URI=$(grep "MONGODB_URI=" .env | cut -d'=' -f2)
        if [[ "$MONGODB_URI" == *"ssl=true"* ]]; then
            check_pass "MongoDB SSL连接已启用"
        else
            check_warn "建议为MongoDB连接启用SSL"
        fi
    else
        check_warn "未找到MONGODB_URI配置，使用默认配置"
    fi
    
else
    check_fail "环境变量文件 .env 不存在，请复制 .env.example 并配置"
fi

echo ""
echo "🔍 检查应用配置..."

# 检查application.yml
if [ -f "api/src/main/resources/application.yml" ]; then
    check_pass "应用配置文件存在"
    
    # 检查CORS配置
    if grep -q "cors:" api/src/main/resources/application.yml; then
        CORS_ORIGINS=$(grep -A 1 "cors:" api/src/main/resources/application.yml | grep "allowed-origins" | cut -d':' -f2 | tr -d ' ')
        if [[ "$CORS_ORIGINS" == "*" ]]; then
            check_warn "CORS配置允许所有域名，生产环境建议限制特定域名"
        else
            check_pass "CORS配置已限制域名"
        fi
    else
        check_warn "未找到CORS配置"
    fi
    
    # 检查Cookie安全配置
    if grep -q "secure: true" api/src/main/resources/application.yml; then
        check_pass "Cookie安全标志已启用"
    else
        check_warn "建议在生产环境启用Cookie安全标志"
    fi
    
else
    check_fail "应用配置文件不存在"
fi

echo ""
echo "🛡️ 检查代码安全..."

# 检查是否有硬编码的密钥
echo "检查硬编码密钥..."
if grep -r "password.*=" --include="*.java" --include="*.yml" --include="*.properties" . | grep -v ".git" | grep -v "example" | grep -v "#" > /dev/null; then
    check_warn "发现可能的硬编码密码，请检查"
    grep -r "password.*=" --include="*.java" --include="*.yml" --include="*.properties" . | grep -v ".git" | grep -v "example" | grep -v "#" | head -5
else
    check_pass "未发现明显的硬编码密码"
fi

# 检查TODO和FIXME
echo "检查待办事项..."
TODO_COUNT=$(grep -r "TODO\|FIXME" --include="*.java" . | grep -v ".git" | wc -l)
if [ $TODO_COUNT -gt 0 ]; then
    check_warn "发现 $TODO_COUNT 个待办事项，建议在部署前处理"
else
    check_pass "未发现待办事项"
fi

echo ""
echo "📦 检查依赖安全..."

# 检查Maven依赖
if command -v mvn &> /dev/null; then
    echo "运行Maven依赖检查..."
    if mvn dependency:tree > /dev/null 2>&1; then
        check_pass "Maven依赖检查通过"
    else
        check_warn "Maven依赖检查失败，请检查pom.xml"
    fi
else
    check_warn "Maven未安装，跳过依赖检查"
fi

echo ""
echo "🔐 检查文件权限..."

# 检查敏感文件权限
if [ -f ".env" ]; then
    ENV_PERMS=$(stat -f "%A" .env 2>/dev/null || stat -c "%a" .env 2>/dev/null)
    if [ "$ENV_PERMS" = "600" ] || [ "$ENV_PERMS" = "644" ]; then
        check_pass "环境变量文件权限正确 ($ENV_PERMS)"
    else
        check_warn "环境变量文件权限可能过于宽松 ($ENV_PERMS)，建议设置为600"
    fi
fi

echo ""
echo "📊 检查结果汇总:"
echo "================================"
echo -e "${GREEN}通过: $PASS${NC}"
echo -e "${YELLOW}警告: $WARN${NC}"
echo -e "${RED}失败: $FAIL${NC}"
echo "================================"

if [ $FAIL -gt 0 ]; then
    echo -e "${RED}❌ 安全检查失败，请修复上述问题后再部署${NC}"
    exit 1
elif [ $WARN -gt 0 ]; then
    echo -e "${YELLOW}⚠️ 安全检查通过，但有警告项需要注意${NC}"
    exit 0
else
    echo -e "${GREEN}✅ 安全检查全部通过，可以部署${NC}"
    exit 0
fi
