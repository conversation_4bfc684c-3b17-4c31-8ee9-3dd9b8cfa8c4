version: '3.8'

services:
  mcmd-backend:
    build:
      context: .
      tags:
        - "mcmd-backend:latest"
    image: mcmd-backend:latest
    container_name: mcmd_backend
    ports:
      - "8081:8081"
    environment:
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION=${JWT_EXPIRATION}
      - JWT_REFRESH_WINDOW=${JWT_REFRESH_WINDOW}
      - TENCENT_COS_SECRET_ID=${TENCENT_COS_SECRET_ID}
      - TENCENT_COS_SECRET_KEY=${TENCENT_COS_SECRET_KEY}
      - TENCENT_COS_BUCKET_NAME=${TENCENT_COS_BUCKET_NAME}
      - TENCENT_COS_REGION=${TENCENT_COS_REGION}
      - TENCENT_COS_DOMAIN=${TENCENT_COS_DOMAIN}
      - LANGCHAIN4J_API_KEY=${LANGCHAIN4J_API_KEY}
      - LANGCHAIN4J_BASE_URL=${LANGCHAIN4J_BASE_URL}
      - LANGCHAIN4J_MODEL_NAME=${LANGCHAIN4J_MODEL_NAME}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - COOKIE_SECURE=${COOKIE_SECURE}
      - COOKIE_DOMAIN=${COOKIE_DOMAIN}
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
