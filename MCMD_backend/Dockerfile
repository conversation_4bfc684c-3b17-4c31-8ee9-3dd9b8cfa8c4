# 使用包含Java和Python的基础镜像
FROM openjdk:21-jdk-slim

# 配置腾讯云Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.cloud.tencent.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.cloud.tencent.com/g' /etc/apt/sources.list.d/debian.sources

# 安装Python和必要的系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制Python脚本和依赖文件
COPY api/src/main/resources/scripts/ /app/scripts/
COPY requirements.txt /app/

# 安装Python依赖（使用腾讯云镜像，允许系统包安装）
RUN pip3 install --no-cache-dir --break-system-packages -i https://mirrors.cloud.tencent.com/pypi/simple/ -r requirements.txt

# 复制JAR文件
COPY api/target/mcmd_backend.jar /app/app.jar

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8081

# 启动应用
CMD ["java", "-jar", "app.jar"]
