package com.tcmp2.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Map;

/**
 * 材料请求DTO
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 16:00
 * @description : 用于验证材料数据的请求对象
 */
@Data
public class MaterialRequest {

    @NotBlank(message = "材料ID不能为空")
    @Size(max = 100, message = "材料ID长度不能超过100个字符")
    private String id;

    @NotBlank(message = "化学式不能为空")
    @Size(max = 200, message = "化学式长度不能超过200个字符")
    private String formula;

    @Size(max = 500, message = "材料名称长度不能超过500个字符")
    private String name;

    @Size(max = 100, message = "空间群长度不能超过100个字符")
    private String spaceGroup;

    @Size(max = 100, message = "点群长度不能超过100个字符")
    private String pointGroup;

    @Size(max = 2000, message = "描述长度不能超过2000个字符")
    private String description;

    @Size(max = 1000, message = "DOI长度不能超过1000个字符")
    private String doi;

    @Size(max = 5000, message = "路径长度不能超过5000个字符")
    private String path;

    // 其他属性可以是动态的
    private Map<String, Object> additionalProperties;

    /**
     * 验证必填字段
     */
    public boolean hasRequiredFields() {
        return id != null && !id.trim().isEmpty() &&
               formula != null && !formula.trim().isEmpty();
    }
}
