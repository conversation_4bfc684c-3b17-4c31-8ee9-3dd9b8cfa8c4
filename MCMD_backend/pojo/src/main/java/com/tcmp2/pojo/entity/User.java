package com.tcmp2.pojo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 用户实体类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 10:00
 * @description : 用户信息实体
 */
@Data
@Document(collection = "users")
public class User {

    @Id
    @Field("_id")
    private String mongoId;

    /** 用户名 */
    @Field("username")
    private String username;

    /** 密码哈希 */
    @Field("password")
    private String password;

    /** 用户角色 */
    @Field("role")
    private String role; // "admin" 或 "user"

    /** 邮箱 */
    @Field("email")
    private String email;

    /** 真实姓名 */
    @Field("realName")
    private String realName;

    /** 机构/组织 */
    @Field("organization")
    private String organization;

    /** 创建时间 */
    @Field("createdAt")
    private Date createdAt;

    /** 最后登录时间 */
    @Field("lastLoginAt")
    private Date lastLoginAt;

    /** 账户状态 */
    @Field("status")
    private String status; // "active", "banned"
}
