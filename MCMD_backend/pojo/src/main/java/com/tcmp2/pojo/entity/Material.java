package com.tcmp2.pojo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Map;

/**
 * 材料实体类
 * 注意：所有数值字段必须是有效的数字，不能包含非数字字符。
 * 如果字段值为空，请使用空字符串""或null，而不是非数字字符。
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 0:06
 */
@Data
@Document(collection = "materials")
public class Material {

    @Id
    @Field("_id")
    private String mongoId;

    /** 材料ID */
    @Field("id")
    private String id;

    /** 材料结构 */
    @Field("Structure")
    private String structure;
    
    /** 化学式 */
    @Field("Formula")
    private String formula;
    
    /** 晶格参数a (必须是数字) */
    @Field("a")
    private Double a;
    
    /** 晶格参数b (必须是数字) */
    @Field("b")
    private Double b;
    
    /** 晶格参数c (必须是数字) */
    @Field("c")
    private Double c;
    
    /** 晶格参数a1 (必须是数字) */
    @Field("a1")
    private Double a1;
    
    /** 晶格参数b1 (必须是数字) */
    @Field("b1")
    private Double b1;
    
    /** 晶格参数c1 (必须是数字) */
    @Field("c1")
    private Double c1;
    
    /** 空间群编号 (必须是整数) */
    @Field("Space_group_num")
    private Integer spaceGroupNum;
    
    /** 空间群符号 */
    @Field("Space_group_sym")
    private String spaceGroupSym;
    
    /** 点群 */
    @Field("Point_group")
    private String pointGroup;

    /** 晶系 */
    @Field("Crystal_system")
    private String crystalSystem;
    
    /** 磁晶格 */
    @Field("magnetic_lattice")
    private String magneticLattice;
    
    /** 密度 */
    @Field("Density")
    private String density;
    
    /** 单位晶胞体积 */
    @Field("V_unit_cell")
    private String VUnitCell;
    
    /** 摩尔质量 */
    @Field("Mole_Mass")
    private String moleMass;
    
    /** 摩尔体积 */
    @Field("Mole_Volume")
    private String moleVolume;
    
    /** GGA能带间隙 */
    @Field("GGA_band_gap")
    private Double GGABandGap;
    
    /** E_hull */
    @Field("E_hull")
    private Double EHull;
    
    /** 磁离子 */
    @Field("Mag_ion")
    private String magIon;
    
    /** 磁离子体积 */
    @Field("Mag_ion_V")
    private String magIonV;
    
    /** 磁离子质量 */
    @Field("Mag_ion_m")
    private String magIonM;
    
    /** 磁矩 */
    @Field("Magnetic_moment")
    private String magneticMoment;
    
    /** 有效自旋 */
    @Field("Effective_Spin")
    private String effectiveSpin;
    
    /** S_GS_Vol */
    @Field("S_GS_Vol")
    private String SGSVol;
    
    /** S_GS_Mass */
    @Field("S_GS_Mass")
    private String SGSMass;
    
    /** 奈尔温度 */
    @Field("T_N")
    private String TN;
    
    /** 访问温度 */
    @Field("T_access")
    private String TAccess;
    
    /** Nm_Rln_2J_1_ */
    @Field("Nm_Rln_2J_1_")
    private String nmRln2J1;
    
    /** S_GS_mol */
    @Field("S_GS_mol")
    private String SGSMol;
    
    /** S_GS_mol2 */
    @Field("S_GS_mol2")
    private String SGSMol2;
    
    /** 面内最近磁离子距离 */
    @Field("Nearest_Mag_Ion_Distance_In_plane")
    private String nearestMagIonDistanceInPlane;
    
    /** 面间最近磁离子距离 */
    @Field("Nearest_Mag_Ion_Distance_inter_plane")
    private String nearestMagIonDistanceInterPlane;
    
    /** DOI */
    @Field("DOI")
    private String doi;

    /** 数据创建者 */
    @Field("createdBy")
    private String createdBy;

    /** 创建时间 */
    @Field("createdAt")
    private java.util.Date createdAt;

    /** 最后修改者 */
    @Field("lastModifiedBy")
    private String lastModifiedBy;

    /** 最后修改时间 */
    @Field("lastModifiedAt")
    private java.util.Date lastModifiedAt;

    // Getter and Setter for customProperties
    /** 自定义属性 */
    @Field("customProperties")
    private Map<String, CustomProperty> customProperties;

    private Map<String, Object> properties; // 其他动态字段

}
