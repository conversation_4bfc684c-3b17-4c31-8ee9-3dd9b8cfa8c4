package com.tcmp2.pojo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 自定义属性实体类
 * 用于存储材料的自定义属性信息
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomProperty {

    /** 属性值 */
    private Object value;

    /** 数据类型 (string, number, boolean) */
    private String type;

    /** 单位 (可选) */
    private String unit;

    /** 描述 (可选) */
    private String description;

    /**
     * 构造函数 - 只包含值和类型
     */
    public CustomProperty(Object value, String type) {
        this.value = value;
        this.type = type;
        this.unit = "";
        this.description = "";
    }

    /**
     * 获取格式化后的值
     * 根据类型返回正确的数据类型
     */
    public Object getFormattedValue() {
        if (value == null) {
            return null;
        }

        switch (type) {
            case "number":
                if (value instanceof Number) {
                    return value;
                } else {
                    try {
                        return Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        return value; // 如果转换失败，返回原值
                    }
                }
            case "boolean":
                if (value instanceof Boolean) {
                    return value;
                } else {
                    return Boolean.parseBoolean(value.toString());
                }
            case "string":
            default:
                return value.toString();
        }
    }

    /**
     * 验证属性是否有效
     */
    public boolean isValid() {
        return value != null && type != null && !type.trim().isEmpty();
    }
}
