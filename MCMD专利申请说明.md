# (19)国家知识产权局

# (12)发明专利申请

(10)申请公布号CN[待分配](43)申请公布日[待定]

(21)申请号[待分配]

(22)申请日[待定]

(71)申请人[申请人信息]地址[申请人地址]

(72)发明人[发明人姓名]

(74)专利代理机构[代理机构名称]

专利代理师[代理师姓名]

(51)Int.Cl.

G06F16/245(2019.01) G06F16/25(2019.01) G06F16/28(2019.01) G06F16/2457(2019.01)

G06N3/045(2023.01) G06T15/08(2011.01) G16C20/90(2019.01)

权利要求书2页 说明书22页 附图4页

# (54)发明名称

一种基于交互式可视化和AI分析的磁热材料数据库系统

# (57)摘要

本申请提供了一种基于交互式可视化和AI分析的磁热材料数据库系统，包括：交互式用户界面模块，提供元素周期表交互选择、多维度搜索和3D晶体结构可视化功能；数据管理模块，采用MongoDB存储磁热材料数据，集成腾讯云COS存储CIF晶体文件；AI分析模块，集成LangChain4J框架，提供流式AI材料分析和实时Markdown渲染；可视化处理模块，结合ChemDoodle和Three.js实现3D晶体结构展示，自动生成XRD衍射谱图。该系统通过创新的技术组合，实现了交互式元素选择与智能搜索的深度融合，3D可视化与AI分析的协同工作，为磁热材料研究提供了集数据管理、可视化分析、智能问答于一体的综合解决方案。

## 系统架构图

```mermaid
graph TB
    subgraph "前端层 - Vue 3 + Vuetify 3"
        A[交互式元素周期表] --> B[高级搜索界面]
        B --> C[3D晶体结构可视化]
        C --> D[XRD谱图展示]
        D --> E[AI材料分析界面]
        E --> F[实时Markdown渲染]
    end
    
    subgraph "API网关层"
        G[REST API Controller]
        H[JWT认证中间件]
        I[CORS安全配置]
        J[访问频率限制]
    end
    
    subgraph "业务服务层 - Spring Boot 3.5"
        K[材料数据管理服务]
        L[用户认证服务]
        M[文件存储服务]
        N[AI分析服务]
        O[XRD计算服务]
        P[权限管理服务]
    end
    
    subgraph "数据存储层"
        Q[(MongoDB数据库)]
        R[腾讯云COS存储]
        S[索引优化]
    end
    
    subgraph "AI集成层"
        T[LangChain4J框架]
        U[流式Markdown处理器]
        V[提示词模板引擎]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H
    H --> I
    I --> J
    
    J --> K
    J --> L
    J --> M
    J --> N
    J --> O
    J --> P
    
    K --> Q
    M --> R
    K --> S
    
    N --> T
    N --> U
    N --> V
```

1. 一种基于交互式可视化和AI分析的磁热材料数据库系统，其特征在于，所述系统包括：

交互式用户界面模块，用于提供基于Vue 3框架的响应式用户界面，包括交互式元素周期表组件、多维度搜索界面和3D晶体结构可视化展示；

数据管理模块，用于采用MongoDB数据库存储磁热材料的晶体结构数据和物理性质参数，集成腾讯云COS对象存储服务管理CIF晶体文件和XRD数据文件；

AI分析模块，用于集成LangChain4J框架连接大语言模型，提供材料属性的智能分析和流式AI响应处理；

可视化处理模块，用于结合ChemDoodle和Three.js技术实现晶体结构的3D交互式展示，自动计算并生成X射线衍射谱图。

2. 根据权利要求1所述的系统，其特征在于，所述交互式用户界面模块包括：

元素周期表交互组件，用于提供118个化学元素的可视化选择界面，支持多元素同时选择和实时状态更新；

高级搜索组件，用于根据化学式、空间群、晶胞参数、磁热性能等多维度条件进行材料筛选；

搜索结果展示组件，用于以表格形式展示搜索结果，支持分页、排序和详细信息查看。

3. 根据权利要求1或2所述的系统，其特征在于，所述数据管理模块采用微服务架构设计，包括：

材料数据服务，用于处理磁热材料的CRUD操作，支持批量数据导入和多格式导出；

文件存储服务，用于管理CIF晶体文件的上传、下载和云端存储，实现文件的安全访问控制；

用户认证服务，用于基于JWT令牌的用户身份验证和基于角色的权限管理。

4. 根据权利要求1所述的系统，其特征在于，所述AI分析模块包括：

流式AI处理单元，用于接收用户的材料分析请求，调用大语言模型进行智能分析；

Markdown流式处理器，用于对AI响应进行实时格式化处理，确保输出内容的格式正确性；

提示词模板引擎，用于根据材料数据和CIF文件内容构建结构化的分析提示词。

5. 根据权利要求4所述的系统，其特征在于，所述流式AI处理单元使用Server-Sent Events技术实现实时响应传输，通过以下步骤处理用户请求：

获取指定材料的结构化数据和CIF文件内容；

使用提示词模板引擎构建包含材料属性和晶体结构信息的分析提示词；

调用LangChain4J框架连接大语言模型，发送分析请求并接收流式响应；

通过Markdown流式处理器对响应内容进行格式修复和优化；

将处理后的内容以SSE流的形式实时推送给前端界面。

6. 根据权利要求1所述的系统，其特征在于，所述可视化处理模块包括：

3D晶体结构渲染单元，用于解析CIF文件格式，使用ChemDoodle库生成晶体结构的三维模型；

XRD谱图计算单元，用于基于晶体结构参数自动计算X射线衍射谱图，支持CuKα、CuKα1、CuKα2多种波长；

交互式图表展示单元，用于使用ECharts库提供可缩放、可交互的XRD谱图展示。

7. 根据权利要求6所述的系统，其特征在于，所述3D晶体结构渲染单元支持超晶胞扩展功能，包括：

晶胞参数解析，从CIF文件中提取晶格常数和原子坐标信息；

超晶胞构建，支持用户自定义nx、ny、nz参数生成扩展的晶体结构；

多种显示模式，提供球棒模型、空间填充模型等多种3D展示方式；

元素颜色映射，根据Jmol标准为不同化学元素分配标准颜色。

8. 根据权利要求1所述的系统，其特征在于，所述系统采用前后端分离的微服务架构，后端使用Java 21和Spring Boot 3.5框架，前端使用Vue 3和Vuetify 3框架，通过以下技术组合实现创新功能：

Vue 3响应式框架与交互式元素周期表的深度集成，实现元素选择状态的实时同步；

MongoDB文档数据库与复杂材料属性查询的优化结合，支持多维度条件的高效检索；

ChemDoodle化学可视化库与Three.js 3D引擎的协同工作，提供高质量的晶体结构展示；

LangChain4J AI框架与流式响应处理的创新结合，实现材料分析的实时交互体验。

9. 根据权利要求8所述的系统，其特征在于，所述系统通过以下安全机制保障数据安全和用户权限管理：

JWT令牌认证机制，实现无状态的用户身份验证；

Argon2密码哈希算法，提供企业级的密码安全保护；

基于角色的权限控制，区分管理员和普通用户的数据访问权限；

CORS跨域安全配置和访问频率限制，防止恶意请求和数据泄露。

10. 根据权利要求1所述的系统，其特征在于，所述系统针对磁热材料研究的特定需求，提供以下专业化功能：

磁热材料属性管理，支持居里温度、磁熵变、绝热温变等专业参数的存储和查询；

磁晶格类型分类，支持三角晶格、Kagome晶格、Shastry-Sutherland晶格等特殊磁性结构的识别；

磁离子分析功能，自动识别和分析材料中的磁性离子及其配位环境；

磁制冷性能评估，基于材料的磁热性质参数提供制冷效率的智能评估。

# 一种基于交互式可视化和AI分析的磁热材料数据库系统

# 技术领域

[0001] 本申请涉及材料科学数据库技术领域，特别涉及一种基于交互式可视化和AI分析的磁热材料数据库系统，更具体地说，涉及集成了交互式用户界面、3D晶体结构可视化、AI智能分析和云存储技术的磁热材料数据管理系统。

# 背景技术

[0002] 磁热材料是一类在外加磁场作用下能够产生显著温度变化的功能材料，在磁制冷技术中具有重要应用价值。随着环保要求的提高和传统制冷技术局限性的显现，磁制冷技术作为一种绿色、高效的制冷方式受到了广泛关注。磁热材料的研究和开发对于推动磁制冷技术的产业化应用具有重要意义。

[0003] 目前，磁热材料的研究主要面临以下挑战：第一，材料数据分散存储，缺乏统一的数据管理平台。研究人员需要从多个数据库和文献中收集材料信息，数据格式不统一，查询效率低下。第二，材料结构可视化手段有限，传统的二维图表难以直观展示复杂的晶体结构和磁性排列。第三，材料性能分析依赖专业知识，初学者和跨领域研究人员难以快速理解材料的磁热性质和应用潜力。

[0004] 现有的材料数据库系统主要存在以下不足：一是用户界面交互性差，多数系统仍采用传统的表单查询方式，用户体验不佳；二是可视化功能单一，缺乏3D晶体结构展示和XRD谱图生成等专业功能；三是缺乏智能分析能力，无法为用户提供材料性能的深度解读和应用建议；四是数据存储和管理方式落后，难以适应大数据时代的存储和查询需求。

[0005] 随着人工智能技术的快速发展，大语言模型在自然语言处理和知识问答方面展现出强大能力。然而，现有的材料数据库系统尚未充分利用AI技术的优势，特别是在材料性能分析、结构解读和应用指导方面缺乏智能化支持。同时，现代Web技术如Vue 3、Three.js等为构建高交互性、高可视化的用户界面提供了技术基础，但在材料科学领域的应用仍然有限。

[0006] 因此，迫切需要开发一种集成了交互式用户界面、3D可视化、AI智能分析和现代数据管理技术的磁热材料数据库系统，以提高材料研究的效率和质量，降低研究门槛，促进磁热材料和磁制冷技术的发展。

# 发明内容

[0007] 为了解决上述技术问题，本申请实施例提供了一种基于交互式可视化和AI分析的磁热材料数据库系统。该系统通过创新的技术组合，实现了交互式元素选择与智能搜索的深度融合，3D可视化与AI分析的协同工作，为磁热材料研究提供了集数据管理、可视化分析、智能问答于一体的综合解决方案。

[0008] 第一方面，本申请实施例提供了一种基于交互式可视化和AI分析的磁热材料数据库系统，系统包括：交互式用户界面模块，用于提供基于Vue 3框架的响应式用户界面，包括交互式元素周期表组件、多维度搜索界面和3D晶体结构可视化展示；数据管理模块，用于采用MongoDB数据库存储磁热材料的晶体结构数据和物理性质参数，集成腾讯云COS对象存储服务管理CIF晶体文件和XRD数据文件；AI分析模块，用于集成LangChain4J框架连接大语言模型，提供材料属性的智能分析和流式AI响应处理；可视化处理模块，用于结合ChemDoodle和Three.js技术实现晶体结构的3D交互式展示，自动计算并生成X射线衍射谱图。

[0009] 在一些可以实现的实施方式中，交互式用户界面模块包括：元素周期表交互组件，用于提供118个化学元素的可视化选择界面，支持多元素同时选择和实时状态更新；高级搜索组件，用于根据化学式、空间群、晶胞参数、磁热性能等多维度条件进行材料筛选；搜索结果展示组件，用于以表格形式展示搜索结果，支持分页、排序和详细信息查看。

[0010] 在一些可以实现的实施方式中，数据管理模块采用微服务架构设计，包括：材料数据服务，用于处理磁热材料的CRUD操作，支持批量数据导入和多格式导出；文件存储服务，用于管理CIF晶体文件的上传、下载和云端存储，实现文件的安全访问控制；用户认证服务，用于基于JWT令牌的用户身份验证和基于角色的权限管理。

[0011] 在一些可以实现的实施方式中，AI分析模块包括：流式AI处理单元，用于接收用户的材料分析请求，调用大语言模型进行智能分析；Markdown流式处理器，用于对AI响应进行实时格式化处理，确保输出内容的格式正确性；提示词模板引擎，用于根据材料数据和CIF文件内容构建结构化的分析提示词。

[0012] 在一些可以实现的实施方式中，流式AI处理单元使用Server-Sent Events技术实现实时响应传输，通过以下步骤处理用户请求：获取指定材料的结构化数据和CIF文件内容；使用提示词模板引擎构建包含材料属性和晶体结构信息的分析提示词；调用LangChain4J框架连接大语言模型，发送分析请求并接收流式响应；通过Markdown流式处理器对响应内容进行格式修复和优化；将处理后的内容以SSE流的形式实时推送给前端界面。

[0013] 在一些可以实现的实施方式中，可视化处理模块包括：3D晶体结构渲染单元，用于解析CIF文件格式，使用ChemDoodle库生成晶体结构的三维模型；XRD谱图计算单元，用于基于晶体结构参数自动计算X射线衍射谱图，支持CuKα、CuKα1、CuKα2多种波长；交互式图表展示单元，用于使用ECharts库提供可缩放、可交互的XRD谱图展示。

[0014] 在一些可以实现的实施方式中，3D晶体结构渲染单元支持超晶胞扩展功能，包括：晶胞参数解析，从CIF文件中提取晶格常数和原子坐标信息；超晶胞构建，支持用户自定义nx、ny、nz参数生成扩展的晶体结构；多种显示模式，提供球棒模型、空间填充模型等多种3D展示方式；元素颜色映射，根据Jmol标准为不同化学元素分配标准颜色。

[0015] 在一些可以实现的实施方式中，系统采用前后端分离的微服务架构，后端使用Java 21和Spring Boot 3.5框架，前端使用Vue 3和Vuetify 3框架，通过以下技术组合实现创新功能：Vue 3响应式框架与交互式元素周期表的深度集成，实现元素选择状态的实时同步；MongoDB文档数据库与复杂材料属性查询的优化结合，支持多维度条件的高效检索；ChemDoodle化学可视化库与Three.js 3D引擎的协同工作，提供高质量的晶体结构展示；LangChain4J AI框架与流式响应处理的创新结合，实现材料分析的实时交互体验。

[0016] 在一些可以实现的实施方式中，系统通过以下安全机制保障数据安全和用户权限管理：JWT令牌认证机制，实现无状态的用户身份验证；Argon2密码哈希算法，提供企业级的密码安全保护；基于角色的权限控制，区分管理员和普通用户的数据访问权限；CORS跨域安全配置和访问频率限制，防止恶意请求和数据泄露。

[0017] 在一些可以实现的实施方式中，系统针对磁热材料研究的特定需求，提供以下专业化功能：磁热材料属性管理，支持居里温度、磁熵变、绝热温变等专业参数的存储和查询；磁晶格类型分类，支持三角晶格、Kagome晶格、Shastry-Sutherland晶格等特殊磁性结构的识别；磁离子分析功能，自动识别和分析材料中的磁性离子及其配位环境；磁制冷性能评估，基于材料的磁热性质参数提供制冷效率的智能评估。

[0018] 本申请实施例提供的基于交互式可视化和AI分析的磁热材料数据库系统具有以下有益效果：

[0019] 在用户体验方面，系统提供了直观的交互式元素周期表，用户可以通过点击元素直接进行材料搜索，大大简化了查询操作。3D晶体结构可视化功能让用户能够直观地观察材料的原子排列和晶体结构，提高了对材料结构的理解效率。

[0020] 在数据管理方面，系统采用MongoDB文档数据库和腾讯云COS对象存储的组合，既保证了结构化数据的高效查询，又实现了大文件的安全云端存储。微服务架构设计提高了系统的可扩展性和维护性。

[0021] 在智能分析方面，系统集成了LangChain4J框架和大语言模型，能够为用户提供材料性能的深度分析和应用建议，降低了专业知识门槛。流式AI响应处理技术实现了实时交互体验，提高了用户满意度。

[0022] 在技术创新方面，系统实现了多项技术的创新组合：Vue 3与交互式元素周期表的深度集成、ChemDoodle与Three.js的协同可视化、LangChain4J与流式响应的AI分析等，为材料科学数据库系统的发展提供了新的技术路径。

# 附图说明

[0023] 为了更清楚地说明本说明书披露的多个实施例的技术方案，下面将对实施例描述中所需要使用的附图作简单地介绍，显而易见地，下面描述中的附图仅仅是本说明书披露的多个实施例，对于本领域普通技术人员来讲，在不付出创造性劳动的前提下，还可以根据这些附图获得其它的附图。

[0024] 图1是本申请实施例提供的基于交互式可视化和AI分析的磁热材料数据库系统的整体架构图；

[0025] 图2是本申请实施例提供的创新技术组合流程图；

[0026] 图3是本申请实施例提供的数据处理与AI分析流程时序图；

[0027] 图4是本申请实施例提供的交互式用户界面模块结构图。

## 创新技术组合流程图

```mermaid
flowchart TD
    A[用户交互] --> B{选择操作类型}

    B -->|元素选择| C[交互式元素周期表]
    B -->|材料搜索| D[多维度搜索引擎]
    B -->|结构查看| E[3D可视化系统]
    B -->|AI分析| F[智能分析系统]

    C --> G[实时元素状态更新]
    G --> H[动态搜索条件构建]

    D --> I[MongoDB复杂查询]
    I --> J[分页结果展示]

    E --> K[CIF文件解析]
    K --> L[ChemDoodle 3D渲染]
    L --> M[XRD谱图计算]

    F --> N[LangChain4J处理]
    N --> O[流式AI响应]
    O --> P[实时Markdown渲染]

    H --> Q[统一结果处理]
    J --> Q
    M --> Q
    P --> Q

    Q --> R[响应式界面展示]
```

## 数据处理与AI分析流程

```mermaid
sequenceDiagram
    participant U as 用户界面
    participant API as API网关
    participant MS as 材料服务
    participant AI as AI分析服务
    participant DB as MongoDB
    participant COS as 腾讯云COS
    participant LLM as 大语言模型

    U->>API: 请求材料AI分析
    API->>API: JWT认证验证
    API->>MS: 获取材料数据
    MS->>DB: 查询材料信息
    DB-->>MS: 返回结构化数据
    MS->>COS: 获取CIF文件
    COS-->>MS: 返回文件内容
    MS-->>API: 材料数据+CIF内容

    API->>AI: 启动AI分析
    AI->>AI: 构建提示词模板
    AI->>LLM: 发送分析请求

    loop 流式响应处理
        LLM-->>AI: 流式AI响应
        AI->>AI: Markdown格式修复
        AI-->>U: 实时渲染内容
    end

    AI-->>API: 分析完成
    API-->>U: 最终结果展示
```

# 具体实施方式

[0028] 为了使本申请实施例的目的、技术方案和优点更加清楚，下面将结合附图，对本申请实施例中的技术方案进行详细描述。

[0029] 在本申请实施例的描述中，"示例性的"、"例如"或者"举例来说"等词用于表示作例子、例证或说明。本申请实施例中被描述为"示例性的"、"例如"或者"举例来说"的任何实施例或设计方案不应被解释为比其它实施例或设计方案更优选或更具优势。确切而言，使用"示例性的"、"例如"或者"举例来说"等词旨在以具体方式呈现相关概念。

[0030] 在本申请实施例的描述中，术语"和/或"，仅仅是一种描述关联对象的关联关系，表示可以存在三种关系，例如，A和/或B，可以表示：单独存在A，单独存在B，同时存在A和B这三种情况。另外，除非另有说明，术语"多个"的含义是指两个或两个以上。

[0031] 本申请实施例所涉及的相关术语：

[0032] Vue 3是一个用于构建用户界面的渐进式JavaScript框架，具有响应式数据绑定和组合式API等特性，为构建现代化Web应用提供了强大的技术基础。

[0033] Vuetify 3是基于Vue 3的Material Design组件库，提供了丰富的UI组件和响应式布局系统，能够快速构建美观且功能完善的用户界面。

[0034] ChemDoodle是一个专业的化学结构可视化JavaScript库，支持2D和3D分子结构的绘制和展示，广泛应用于化学和材料科学领域。

[0035] Three.js是一个基于WebGL的3D图形库，提供了丰富的3D渲染功能，能够在Web浏览器中创建复杂的三维场景和动画效果。

[0036] LangChain4J是一个Java版本的LangChain框架，用于构建基于大语言模型的应用程序，提供了链式调用、提示词模板、流式响应等功能。

[0037] MongoDB是一个基于文档的NoSQL数据库，支持灵活的数据模型和复杂查询，特别适合存储结构多样的材料科学数据。

[0038] 腾讯云COS（Cloud Object Storage）是腾讯云提供的对象存储服务，具有高可靠性、高可用性和高扩展性，适合存储大量的文件数据。

[0039] CIF（Crystallographic Information File）是晶体学信息文件格式，包含了晶体结构的详细信息，如晶胞参数、原子坐标、空间群等，是材料科学研究中的标准数据格式。

[0040] XRD（X-ray Diffraction）是X射线衍射技术，通过分析X射线在晶体中的衍射现象来确定晶体结构，是材料表征的重要手段。

[0041] 图1是本申请实施例提供的基于交互式可视化和AI分析的磁热材料数据库系统的整体架构图。如图1所示，系统采用分层架构设计，包括前端层、API网关层、业务服务层、数据存储层和AI集成层。

[0042] 前端层基于Vue 3和Vuetify 3框架构建，包括交互式元素周期表、高级搜索界面、3D晶体结构可视化、XRD谱图展示、AI材料分析界面和实时Markdown渲染等核心组件。这些组件通过响应式数据绑定实现了高度的交互性和用户体验优化。

[0043] API网关层负责处理前端请求，包括REST API Controller、JWT认证中间件、CORS安全配置和访问频率限制等安全机制，确保系统的安全性和稳定性。

[0044] 业务服务层采用Spring Boot 3.5框架和微服务架构，包括材料数据管理服务、用户认证服务、文件存储服务、AI分析服务、XRD计算服务和权限管理服务等核心业务模块。

[0045] 数据存储层采用MongoDB数据库存储结构化的材料数据，集成腾讯云COS存储CIF文件和XRD数据文件，通过索引优化提高查询性能。

[0046] AI集成层包括LangChain4J框架、流式Markdown处理器和提示词模板引擎，为系统提供智能分析能力。

[0047] 图2为本申请实施例提供的创新技术组合流程图。如图2所示，系统通过多种创新技术的组合实现了高效的用户交互和数据处理流程。用户可以通过交互式元素周期表进行元素选择，系统实时更新元素状态并动态构建搜索条件；通过多维度搜索引擎进行材料筛选，利用MongoDB的复杂查询能力实现高效检索；通过3D可视化系统查看晶体结构，结合CIF文件解析、ChemDoodle 3D渲染和XRD谱图计算提供全面的结构信息；通过智能分析系统获取AI分析结果，利用LangChain4J处理、流式AI响应和实时Markdown渲染实现智能问答功能。

[0048] 图3为本申请实施例提供的数据处理与AI分析流程时序图。如图3所示，当用户请求材料AI分析时，系统首先进行JWT认证验证，然后获取材料数据和CIF文件内容，构建提示词模板并发送给大语言模型，通过流式响应处理实现实时的AI分析结果展示。

[0049] 交互式用户界面模块的具体实现：

[0050] 元素周期表交互组件采用Vue 3的响应式数据绑定技术，为118个化学元素创建可点击的交互式界面。每个元素具有原子序数、元素符号、元素名称和元素类别等属性，通过CSS样式和JavaScript事件处理实现元素的选择状态管理。当用户点击元素时，系统自动更新选中状态并触发搜索条件的动态构建。

[0051] 高级搜索组件提供多维度的搜索条件设置，包括化学式精确匹配或包含匹配、空间群编号范围、晶胞参数范围、磁晶格类型、磁热性能参数等。搜索组件采用Vuetify 3的表单组件构建，支持实时验证和条件组合。

[0052] 搜索结果展示组件使用Vuetify 3的数据表格组件，支持分页显示、列排序、行选择和详细信息展开等功能。表格数据通过Vue 3的响应式系统与后端API保持同步，实现了高效的数据展示和交互。

[0053] 数据管理模块的具体实现：

[0054] 材料数据服务基于Spring Boot 3.5框架开发，采用Repository模式和Service层架构。材料实体类包含id、化学式、晶格参数、空间群、磁热性能等属性，通过MongoDB的文档存储特性支持灵活的数据结构。服务层提供CRUD操作接口，支持单个材料的增删改查以及批量数据的导入导出。

[0055] 文件存储服务集成腾讯云COS SDK，实现CIF文件的上传、下载和管理功能。服务采用单例模式管理COS客户端连接，通过对象键（Object Key）的命名规则实现文件的分类存储。文件上传时自动设置元数据信息，包括文件类型、大小和上传时间等。

[0056] 用户认证服务基于JWT令牌实现无状态认证，采用Argon2算法进行密码哈希处理。服务支持用户注册、登录、令牌刷新和权限验证等功能，通过角色管理实现不同用户的权限控制。

[0057] AI分析模块的具体实现：

[0058] 流式AI处理单元采用LangChain4J框架连接大语言模型，通过OpenAI兼容的API接口发送分析请求。处理单元首先获取指定材料的结构化数据和CIF文件内容，然后使用提示词模板引擎构建包含材料属性和晶体结构信息的分析提示词。

[0059] Markdown流式处理器负责对AI响应进行实时格式化处理，解决流式输出中可能出现的格式问题。处理器采用正则表达式和状态机技术，对标题、列表、代码块、表格等Markdown元素进行识别和修复，确保输出内容的格式正确性。

[0060] 提示词模板引擎根据材料数据的类型和内容动态构建分析提示词。模板引擎支持变量替换、条件判断和循环处理等功能，能够根据不同的材料特性生成针对性的分析提示词。

[0061] 可视化处理模块的具体实现：

[0062] 3D晶体结构渲染单元首先解析CIF文件格式，提取晶格常数、原子坐标、空间群等结构信息。然后使用ChemDoodle库创建3D分子模型，支持球棒模型、空间填充模型等多种显示方式。渲染单元还支持超晶胞扩展功能，用户可以通过nx、ny、nz参数生成扩展的晶体结构。

[0063] XRD谱图计算单元基于晶体结构参数和衍射理论，自动计算X射线衍射谱图。计算单元支持CuKα、CuKα1、CuKα2等多种X射线波长，通过布拉格定律和结构因子计算衍射峰的位置和强度。

[0064] 交互式图表展示单元使用ECharts库创建可缩放、可交互的XRD谱图。图表支持数据缩放、峰值标注、波长切换等功能，为用户提供专业的谱图分析工具。

[0065] 实施例1：

[0066] 用户通过交互式元素周期表选择Fe（铁）和Ni（镍）元素，系统自动构建搜索条件"Fe Ni"并执行材料搜索。搜索结果显示包含铁和镍元素的磁热材料列表，用户点击某个材料（如Fe2NiGa）查看详细信息。

[0067] 系统从MongoDB数据库中查询Fe2NiGa的结构化数据，包括晶格参数（a=5.82Å, b=5.82Å, c=5.82Å）、空间群（Fm-3m）、居里温度（350K）等信息。同时从腾讯云COS获取对应的CIF文件内容。

[0068] 3D晶体结构渲染单元解析CIF文件，使用ChemDoodle库生成Fe2NiGa的三维晶体结构模型。用户可以通过鼠标操作旋转、缩放和平移结构模型，观察原子的空间排列。

[0069] XRD谱图计算单元基于晶体结构参数计算X射线衍射谱图，显示主要衍射峰的位置和强度。用户可以切换不同的X射线波长，比较谱图差异。

[0070] 用户点击AI分析按钮，系统调用AI分析模块对Fe2NiGa进行智能分析。AI分析结果以流式方式实时显示，包括材料的磁性机理、磁热效应分析、应用前景评估等专业内容。

[0071] 实施例2：

[0072] 研究人员需要查找具有Kagome磁晶格结构的磁热材料。用户在高级搜索界面中选择磁晶格类型为"Kagome"，设置居里温度范围为200-400K，点击搜索按钮。

[0073] 系统构建MongoDB查询条件，在材料数据库中检索满足条件的材料。查询结果显示多个具有Kagome结构的磁热材料，如YMn6Sn6、TbMn6Sn6等。

[0074] 用户选择YMn6Sn6材料查看详细信息，系统展示其晶体结构、磁热性能参数和相关文献信息。通过3D可视化功能，用户可以清楚地观察到Kagome晶格的特殊几何结构。

[0075] AI分析功能为用户提供了YMn6Sn6材料的深度分析，包括Kagome晶格对磁性的影响、磁阻挫效应的机理、以及在磁制冷应用中的优势等专业解读。

[0076] 本申请实施例提供的基于交互式可视化和AI分析的磁热材料数据库系统，通过创新的技术组合和系统架构设计，实现了材料数据管理、可视化分析和智能问答的深度融合，为磁热材料研究提供了高效、智能、易用的综合解决方案，具有重要的科学价值和应用前景。
