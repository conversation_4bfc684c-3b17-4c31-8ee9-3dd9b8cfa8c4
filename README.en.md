# MCMD (Magnetocaloric Materials Database)

## Project Introduction
MCMD is a database management system designed specifically for magnetocaloric materials research, dedicated to providing crystal structure data storage, query, visualization, and analysis capabilities for magnetocaloric materials. This system primarily targets materials science researchers and students, helping them efficiently manage, analyze, and utilize magnetocaloric material data to promote the development of magnetocaloric materials research and magnetic refrigeration technology.

## System Architecture
The system adopts a microservice architecture with front-end and back-end separation:
- **Frontend**: Built with Vue 3 framework and Vuetify 3 component library for modern responsive user interface
- **Backend**: Developed using Java 21 + Spring Boot 3.5 with multi-module Maven project structure
- **Database**: MongoDB for storing crystal structure and magnetocaloric property data with optimized indexing and complex query support
- **Storage**: Integrated with Tencent Cloud Object Storage (COS) for secure storage of CIF crystal files and XRD data files
- **Security**: Integrated JWT authentication, Argon2 password encryption, CORS security configuration, and rate limiting

## Core Features

### Data Management & Storage
- **Material Data Management**: Support for batch uploading, storing, and managing crystal files in CIF format
- **Cloud File Storage**: Integrated with Tencent Cloud COS for secure cloud storage and access of crystal files
- **Data Import/Export**: Support for batch import of material data and multi-format export functionality

### Visualization & Analysis
- **Crystal Structure Visualization**: Interactive 3D crystal structure display using ChemDoodleWeb and Three.js
- **XRD Spectrum Generation**: Automatic calculation and display of X-ray diffraction spectra with support for multiple wavelengths (CuKα, CuKα1, CuKα2)
- **Data Chart Display**: Rich data visualization charts using ECharts and Chart.js

### Search & Query
- **Advanced Search Functionality**: Multi-dimensional precise search by chemical formula, space group, cell parameters, magnetocaloric properties, etc.
- **Intelligent Search**: Integrated with LangChain4J for AI-driven intelligent material query and analysis
- **Periodic Table Query**: Interactive periodic table with element selection and material filtering

### Security & Permissions
- **User Authentication System**: JWT-based secure authentication with user registration, login, and permission management
- **Role-based Access Control**: Distinction between regular users and administrator permissions for data access control
- **Security Protection**: Integrated rate limiting, CORS security configuration, and password encryption protection

## Technical Features

### Architecture Design
- **Microservice Architecture**: Backend uses multi-module Maven project structure including API, Service, DAO, POJO, and Common modules
- **Modern Technology Stack**: Frontend with Vue 3 + Vuetify 3, backend with Spring Boot 3.5 + Java 21
- **Responsive Design**: Support for multiple devices and screen sizes with excellent user experience

### Data Processing
- **High-Performance Database**: MongoDB with optimized indexing for complex queries and large-scale data processing
- **Intelligent Analysis**: Integrated LangChain4J framework for AI-driven material analysis and query functionality
- **Multi-Format Support**: Support for CIF file parsing, XRD data processing, and multiple data export formats

### Visualization Technology
- **3D Crystal Structures**: Interactive 3D crystal structure display using ChemDoodleWeb and Three.js
- **Chart Visualization**: Rich data charts and statistical analysis using ECharts and Chart.js
- **Responsive Interface**: Vuetify Material Design based interface adapted for various device screens

### Security & Performance
- **Enterprise-Grade Security**: JWT authentication, Argon2 password encryption, CORS configuration, and rate limiting
- **Cloud Storage Integration**: Tencent Cloud COS object storage ensuring file security and high availability
- **Performance Optimization**: Frontend code splitting, backend caching strategies, and database index optimization

## Installation and Deployment

### Frontend Deployment
```bash
cd MCMD_frontend
npm install
npm run build
```

### Backend Deployment
```bash
cd MCMD_backend
mvn clean package
java -jar api/target/mcmd_backend.jar
```

### Docker Deployment
Both frontend and backend provide Dockerfiles for quick image building:
```bash
# Build frontend image
cd MCMD_frontend
docker build -t mcmd-frontend .

# Build backend image
cd MCMD_backend
docker build -t mcmd-backend .
```

## Technology Stack Details

### Frontend Technologies
- **Framework**: Vue 3.5.16 + Vue Router 4.2.4
- **UI Components**: Vuetify 3.3.16 (Material Design)
- **Chart Libraries**: ECharts 5.6.0 + Chart.js 4.4.0 + Vue-ChartJS 5.2.0
- **3D Visualization**: Three.js 0.158.0 + ChemDoodleWeb
- **Utility Libraries**: Axios 1.6.2, JSZip 3.10.1, Marked 15.0.12
- **Development Tools**: Vue CLI 5.0.8, Sass 1.69.0, ESLint

### Backend Technologies
- **Core Framework**: Spring Boot 3.5.0 + Java 21
- **Database**: Spring Data MongoDB 3.5.0
- **Security & Authentication**: JWT (JJWT 0.11.5) + Argon2 2.11
- **Cloud Storage**: Tencent Cloud COS API 5.6.227
- **AI Integration**: LangChain4J 1.0.1 (OpenAI Spring Boot Starter)
- **Build Tool**: Maven Multi-module Project Structure

### Deployment & Operations
- **Containerization**: Docker + Dockerfile
- **Database**: MongoDB Index Optimization Scripts
- **Security**: CORS Configuration, Rate Limiting, Security Check Scripts

## Development Team
- Professor Yan Qingbo, School of Materials Science and Optoelectronics Technology, University of Chinese Academy of Sciences

## License
This project is licensed under the MIT License