{"name": "mcmd-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@mdi/font": "^7.3.67", "axios": "^1.6.2", "chart.js": "^4.4.0", "echarts": "^5.6.0", "highlight.js": "^11.9.0", "jszip": "^3.10.1", "marked": "^15.0.12", "roboto-fontface": "*", "three": "^0.158.0", "vue": "^3.5.16", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.4", "vuetify": "^3.3.16"}, "devDependencies": {"@babel/eslint-parser": "^7.25.9", "@babel/preset-env": "^7.26.9", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "ejs": "^3.1.10", "eslint-plugin-vue": "^9.32.0", "html-minifier-terser": "^7.2.0", "json5": "^2.2.3", "nth-check": "^2.1.1", "sass": "^1.69.0", "sass-loader": "^13.3.0", "ssri": "^12.0.0", "tough-cookie": "^5.1.2", "webpack-bundle-analyzer": "^4.10.2"}, "overrides": {"postcss": "^8.4.31", "cross-spawn": "^7.0.3"}}