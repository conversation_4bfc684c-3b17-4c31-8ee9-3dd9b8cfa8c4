import axios from 'axios'
import config from '@/config'
import router from '@/router'
import { auth } from './auth'

// 创建axios实例
const api = axios.create({
  baseURL: config.apiBaseUrl,
  withCredentials: true // 允许跨域请求携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 不再需要手动添加Authorization头，因为cookie会自动发送
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    // 处理未授权错误
    if (error.response && error.response.status === 401) {
      // 统一清除认证状态
      sessionStorage.removeItem('username')
      sessionStorage.removeItem('userRole')
      auth.username = null
      auth.userRole = null

      // 触发全局登出事件，确保所有组件状态同步
      window.dispatchEvent(new CustomEvent('auth-logout'))

      // 如果不是在登录页面，则重定向到登录页面
      if (router.currentRoute.value && router.currentRoute.value.path !== '/login') {
        const currentPath = router.currentRoute.value.fullPath || '/'
        router.push({
          path: '/login',
          query: { redirect: currentPath }
        })
      }

      return Promise.reject(new Error('Session expired, please login again'))
    }
    
    // 处理其他错误
    return Promise.reject(error)
  }
)

export const searchApi = {
  getCount() {
    return api.get('/data-count')
  },
  
  searchMaterials(params) {
    return api.get('/materials/search', { params })
  },
  
  getMaterialDetail(id) {
    return api.get(`/materials/${id}`)
  }
}

export const materialApi = {
  list() {
    return api.get('/materials')
  },
  
  create(data) {
    return api.post('/materials', data)
  },
  
  update(id, data) {
    return api.put(`/materials/${id}`, data)
  },
  
  delete(id) {
    return api.delete(`/materials/${id}`)
  }
}

export const authApi = {
  login(credentials) {
    return api.post('/login', credentials)
  },

  logout() {
    return api.post('/logout')
  },

  getUser() {
    return api.get('/user/info')
  },

  getCurrentUserInfo() {
    return api.get('/auth/user-info')
  },

  checkMaterialPermission(materialId) {
    return api.get(`/auth/material-permission/${materialId}`)
  },

  refreshToken() {
    return api.post('/refresh-token')
  }
}

// 用户管理API
export const userApi = {
  getAllUsers() {
    return api.get('/user/list')
  },

  createUser(userData) {
    return api.post('/user/create', userData)
  },

  updateUser(username, userData) {
    return api.put(`/user/${username}`, userData)
  },

  updateUserRole(username, role) {
    return api.put(`/user/${username}/role`, { role })
  },

  updateUserStatus(username, status) {
    return api.put(`/user/${username}/status`, { status })
  },

  getUserInfo() {
    return api.get('/user/info')
  },

  changePassword(passwordData) {
    return api.put('/user/change-password', passwordData)
  },

  resetPassword(username) {
    return api.post(`/user/${username}/reset-password`)
  }
}

export default api
