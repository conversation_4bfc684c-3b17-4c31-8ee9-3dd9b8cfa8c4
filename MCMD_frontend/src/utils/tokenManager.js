/**
 * JWT Token管理工具类
 * 实现LocalStorage + 7天过期机制
 */
class TokenManager {
  static TOKEN_KEY = 'mcmd_jwt_token'
  static EXPIRES_KEY = 'mcmd_jwt_expires'
  static DEFAULT_EXPIRATION_DAYS = 7

  /**
   * 存储token，设置7天过期
   * @param {string} token - JWT token
   * @param {number} expirationDays - 过期天数，默认7天
   */
  static setToken(token, expirationDays = this.DEFAULT_EXPIRATION_DAYS) {
    if (!token) {
      console.warn('TokenManager: 尝试存储空token')
      return
    }

    try {
      const expirationTime = Date.now() + (expirationDays * 24 * 60 * 60 * 1000)
      
      localStorage.setItem(this.TOKEN_KEY, token)
      localStorage.setItem(this.EXPIRES_KEY, expirationTime.toString())
      
      console.log(`Token已存储，${expirationDays}天后过期`)
    } catch (error) {
      console.error('存储token失败:', error)
    }
  }

  /**
   * 获取token
   * @returns {string|null} token或null
   */
  static getToken() {
    try {
      const token = localStorage.getItem(this.TOKEN_KEY)
      const expirationTime = localStorage.getItem(this.EXPIRES_KEY)
      
      if (!token || !expirationTime) {
        return null
      }
      
      // 检查是否过期
      if (Date.now() > parseInt(expirationTime)) {
        console.log('Token已过期，清除存储')
        this.clearToken()
        return null
      }
      
      return token
    } catch (error) {
      console.error('获取token失败:', error)
      return null
    }
  }

  /**
   * 检查token是否有效（存在且未过期）
   * @returns {boolean} 是否有效
   */
  static isTokenValid() {
    return this.getToken() !== null
  }

  /**
   * 清除token
   */
  static clearToken() {
    try {
      localStorage.removeItem(this.TOKEN_KEY)
      localStorage.removeItem(this.EXPIRES_KEY)
      console.log('Token已清除')
    } catch (error) {
      console.error('清除token失败:', error)
    }
  }

  /**
   * 获取token剩余有效时间（毫秒）
   * @returns {number} 剩余时间，-1表示无效或已过期
   */
  static getRemainingTime() {
    try {
      const expirationTime = localStorage.getItem(this.EXPIRES_KEY)
      
      if (!expirationTime) {
        return -1
      }
      
      const remaining = parseInt(expirationTime) - Date.now()
      return remaining > 0 ? remaining : -1
    } catch (error) {
      console.error('获取剩余时间失败:', error)
      return -1
    }
  }

  /**
   * 获取token剩余有效天数
   * @returns {number} 剩余天数，-1表示无效或已过期
   */
  static getRemainingDays() {
    const remaining = this.getRemainingTime()
    if (remaining <= 0) {
      return -1
    }
    
    return Math.ceil(remaining / (24 * 60 * 60 * 1000))
  }

  /**
   * 刷新token过期时间（重新设置为7天）
   * @param {number} expirationDays - 过期天数，默认7天
   */
  static refreshExpiration(expirationDays = this.DEFAULT_EXPIRATION_DAYS) {
    const token = localStorage.getItem(this.TOKEN_KEY)
    if (token) {
      this.setToken(token, expirationDays)
      console.log(`Token过期时间已刷新为${expirationDays}天`)
    }
  }

  /**
   * 获取token信息（用于调试）
   * @returns {object} token信息
   */
  static getTokenInfo() {
    const token = localStorage.getItem(this.TOKEN_KEY)
    const expirationTime = localStorage.getItem(this.EXPIRES_KEY)
    
    if (!token || !expirationTime) {
      return { exists: false }
    }
    
    const remaining = this.getRemainingTime()
    const isValid = remaining > 0
    
    return {
      exists: true,
      isValid,
      expirationTime: new Date(parseInt(expirationTime)),
      remainingTime: remaining,
      remainingDays: this.getRemainingDays()
    }
  }
}

export default TokenManager
