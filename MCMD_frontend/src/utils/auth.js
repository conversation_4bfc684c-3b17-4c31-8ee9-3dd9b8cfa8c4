import axios from 'axios'
import config from '@/config'
import TokenManager from './tokenManager.js'

// 设置axios默认配置
axios.defaults.baseURL = config.apiBaseUrl
axios.defaults.withCredentials = true // 允许跨域请求携带cookie

// 解析JWT令牌 - 导出以供其他模块使用
export function parseJwt(token) {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    }).join(''))
    return JSON.parse(jsonPayload)
  } catch (e) {
    return null
  }
}

class Auth {
  constructor() {
    this.username = sessionStorage.getItem('username')
    this.userRole = sessionStorage.getItem('userRole')
    this.refreshing = false

    // 如果sessionStorage中没有用户信息，但有有效的token，尝试恢复登录状态
    if (!this.username && TokenManager.isTokenValid()) {
      console.log('发现有效的本地token，尝试恢复登录状态')
      this.restoreUserInfoFromToken()
    }
  }

  isLoggedIn() {
    // 检查用户名和有效的JWT token（支持7天免登录）
    const hasUsername = !!this.username
    const hasValidToken = TokenManager.isTokenValid()

    // 如果有有效的token，认为已登录（7天免登录功能）
    if (hasValidToken) {
      // 如果没有用户名，尝试从token中解析或使用默认值
      if (!hasUsername) {
        // 尝试从sessionStorage恢复
        this.username = sessionStorage.getItem('username')
        this.userRole = sessionStorage.getItem('userRole')

        // 如果还是没有，使用默认值（从token解析或设置默认）
        if (!this.username) {
          this.username = 'User' // 临时用户名，后续可以通过API获取真实用户信息
          this.userRole = 'user'
        }
      }
      return true
    }

    // 如果没有有效token，检查是否有用户名（兼容cookie模式）
    return hasUsername
  }

  getUserRole() {
    return this.userRole || 'user'
  }

  isAdmin() {
    return this.getUserRole() === 'admin'
  }

  isUser() {
    return this.getUserRole() === 'user'
  }

  async login(username, password) {
    try {
      const response = await axios.post('/login', {
        username,
        password
      })

      if (response.data.message === 'Login successful') {
        // 使用后端返回的真实用户名，而不是用户输入的用户名/邮箱
        this.username = response.data.username || username
        this.userRole = response.data.role || 'user'

        // 存储用户名和角色
        sessionStorage.setItem('username', this.username)
        sessionStorage.setItem('userRole', this.userRole)

        // 如果后端返回了JWT token，存储到localStorage（7天过期）
        if (response.data.token) {
          TokenManager.setToken(response.data.token, 7)
          console.log('JWT token已存储到localStorage，7天后过期')
        }

        // 触发登录成功事件
        window.dispatchEvent(new CustomEvent('login-success'))

        return {
          success: true,
          message: 'Login successful'
        }
      }
      
      return {
        success: false,
        message: response.data.message
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed, please try again later'
      }
    }
  }

  async logout() {
    try {
      await axios.post('/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.username = null
      this.userRole = null
      sessionStorage.removeItem('username')
      sessionStorage.removeItem('userRole')

      // 清除localStorage中的JWT token
      TokenManager.clearToken()

      // 刷新页面以确保状态更新
      window.location.reload()
    }
  }

  /**
   * 从token恢复用户信息
   */
  async restoreUserInfoFromToken() {
    try {
      const token = TokenManager.getToken()
      if (!token) return

      // 尝试从后端获取用户信息
      const response = await axios.get('/auth/user-info', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.data && response.data.username) {
        this.username = response.data.username
        this.userRole = response.data.role || 'user'

        // 更新sessionStorage
        sessionStorage.setItem('username', this.username)
        sessionStorage.setItem('userRole', this.userRole)

        console.log('用户信息已从token恢复:', this.username)
      }
    } catch (error) {
      console.warn('从token恢复用户信息失败:', error)
      // 如果获取用户信息失败，可能token已失效，清除它
      if (error.response && error.response.status === 401) {
        TokenManager.clearToken()
      }
    }
  }

  getAuthHeader() {
    // 优先从localStorage获取token（7天免登录功能）
    const token = TokenManager.getToken()
    if (token) {
      return {
        'Authorization': `Bearer ${token}`
      }
    }

    // 如果没有localStorage token，依赖cookie（会自动发送）
    return {}
  }
  
  // 刷新令牌
  async refreshToken() {
    // 如果已经在刷新中，则返回
    if (this.refreshing) return
    
    try {
      this.refreshing = true
      
      await axios.post('/refresh-token')
      console.log('Token refreshed')
    } catch (error) {
      console.error('Token refresh failed:', error)
      
      // 如果刷新失败且是因为令牌无效，则登出
      if (error.response && error.response.status === 401) {
        this.logout()
      }
    } finally {
      this.refreshing = false
    }
  }
}

export const auth = new Auth()

// 注意：不在这里设置全局axios拦截器，避免与api.js中的拦截器冲突
// 所有的拦截器逻辑都在api.js中统一处理
