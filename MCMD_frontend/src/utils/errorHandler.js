/**
 * 前端统一错误处理工具
 * <AUTHOR>
 * @createDate 2025-01-20 15:30
 * @description 提供标准化的错误处理和用户友好的错误信息
 */

// 错误类型常量
export const ERROR_TYPES = {
  VALIDATION: 'VALIDATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  DUPLICATE: 'DUPLICATE_ERROR',
  FILE_OPERATION: 'FILE_OPERATION_ERROR',
  DATABASE: 'DATABASE_ERROR',
  NETWORK: 'NETWORK_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// 用户友好的错误消息映射
const USER_FRIENDLY_MESSAGES = {
  [ERROR_TYPES.VALIDATION]: '输入数据格式不正确，请检查后重试',
  [ERROR_TYPES.AUTHENTICATION]: '身份验证失败，请重新登录',
  [ERROR_TYPES.AUTHORIZATION]: '您没有权限执行此操作',
  [ERROR_TYPES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_TYPES.DUPLICATE]: '数据已存在，请检查后重试',
  [ERROR_TYPES.FILE_OPERATION]: '文件操作失败，请重试',
  [ERROR_TYPES.DATABASE]: '数据库操作失败，请稍后重试',
  [ERROR_TYPES.NETWORK]: '网络连接失败，请检查网络后重试',
  [ERROR_TYPES.UNKNOWN]: '系统繁忙，请稍后重试'
}

// 特定错误消息映射
const SPECIFIC_ERROR_MESSAGES = {
  'ID重复': '材料ID已存在，请使用不同的ID',
  'already exists': '数据已存在，请检查后重试',
  'Formula': '缺少化学式字段，请完善材料信息',
  'CIF': 'CIF文件格式错误或缺失',
  'JSON': 'JSON数据格式错误，请检查数据结构',
  'Session expired': '登录会话已过期，请重新登录',
  'Token': '身份验证失败，请重新登录',
  'Permission': '您没有权限执行此操作',
  'Network Error': '网络连接失败，请检查网络连接',
  'timeout': '请求超时，请稍后重试'
}

/**
 * 根据错误信息获取错误类型
 * @param {Error|Object} error - 错误对象
 * @returns {string} 错误类型
 */
export function getErrorType(error) {
  if (!error) return ERROR_TYPES.UNKNOWN

  const message = error.message || error.toString() || ''
  const responseData = error.response?.data?.message || ''
  const fullMessage = (message + ' ' + responseData).toLowerCase()

  if (fullMessage.includes('validation') || 
      fullMessage.includes('invalid') || 
      fullMessage.includes('format')) {
    return ERROR_TYPES.VALIDATION
  }

  if (fullMessage.includes('authentication') || 
      fullMessage.includes('login') || 
      fullMessage.includes('token') || 
      fullMessage.includes('session')) {
    return ERROR_TYPES.AUTHENTICATION
  }

  if (fullMessage.includes('authorization') || 
      fullMessage.includes('permission') || 
      fullMessage.includes('access denied')) {
    return ERROR_TYPES.AUTHORIZATION
  }

  if (fullMessage.includes('not found') || 
      fullMessage.includes('does not exist') || 
      error.response?.status === 404) {
    return ERROR_TYPES.NOT_FOUND
  }

  if (fullMessage.includes('duplicate') || 
      fullMessage.includes('already exists') || 
      fullMessage.includes('id重复')) {
    return ERROR_TYPES.DUPLICATE
  }

  if (fullMessage.includes('file') || 
      fullMessage.includes('upload') || 
      fullMessage.includes('cif')) {
    return ERROR_TYPES.FILE_OPERATION
  }

  if (fullMessage.includes('database') || 
      fullMessage.includes('connection') || 
      error.response?.status >= 500) {
    return ERROR_TYPES.DATABASE
  }

  if (fullMessage.includes('network') || 
      fullMessage.includes('timeout') || 
      !error.response) {
    return ERROR_TYPES.NETWORK
  }

  return ERROR_TYPES.UNKNOWN
}

/**
 * 获取用户友好的错误消息
 * @param {Error|Object} error - 错误对象
 * @returns {string} 用户友好的错误消息
 */
export function getUserFriendlyMessage(error) {
  if (!error) return USER_FRIENDLY_MESSAGES[ERROR_TYPES.UNKNOWN]

  const message = error.message || error.response?.data?.message || ''
  
  // 检查特定错误消息
  for (const [key, value] of Object.entries(SPECIFIC_ERROR_MESSAGES)) {
    if (message.includes(key)) {
      return value
    }
  }

  // 根据错误类型返回通用消息
  const errorType = getErrorType(error)
  return USER_FRIENDLY_MESSAGES[errorType] || USER_FRIENDLY_MESSAGES[ERROR_TYPES.UNKNOWN]
}

/**
 * 统一的错误处理函数
 * @param {Error|Object} error - 错误对象
 * @param {Object} options - 选项
 * @param {boolean} options.showConsole - 是否在控制台显示详细错误
 * @param {string} options.context - 错误上下文信息
 * @returns {Object} 标准化的错误信息
 */
export function handleError(error, options = {}) {
  const { showConsole = false, context = '' } = options
  
  const errorType = getErrorType(error)
  const userMessage = getUserFriendlyMessage(error)
  
  const errorInfo = {
    type: errorType,
    message: userMessage,
    originalError: error,
    context,
    timestamp: new Date().toISOString()
  }

  // 在开发环境或明确要求时显示详细错误
  if (showConsole || process.env.NODE_ENV === 'development') {
    console.group(`🚨 Error in ${context || 'Application'}`)
    console.error('Error Type:', errorType)
    console.error('User Message:', userMessage)
    console.error('Original Error:', error)
    if (error.response) {
      console.error('Response Status:', error.response.status)
      console.error('Response Data:', error.response.data)
    }
    console.groupEnd()
  }

  return errorInfo
}

/**
 * 创建错误提示组件的数据
 * @param {Error|Object} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {Object} 错误提示数据
 */
export function createErrorAlert(error, context = '') {
  const errorInfo = handleError(error, { context })
  
  return {
    show: true,
    type: 'error',
    title: '操作失败',
    message: errorInfo.message,
    timeout: 5000
  }
}

/**
 * 判断是否为用户操作错误（非系统错误）
 * @param {Error|Object} error - 错误对象
 * @returns {boolean} 是否为用户错误
 */
export function isUserError(error) {
  const errorType = getErrorType(error)
  return [
    ERROR_TYPES.VALIDATION,
    ERROR_TYPES.AUTHENTICATION,
    ERROR_TYPES.AUTHORIZATION,
    ERROR_TYPES.NOT_FOUND,
    ERROR_TYPES.DUPLICATE
  ].includes(errorType)
}

/**
 * 获取错误的严重程度
 * @param {Error|Object} error - 错误对象
 * @returns {string} 严重程度 (low, medium, high, critical)
 */
export function getErrorSeverity(error) {
  const errorType = getErrorType(error)
  
  switch (errorType) {
    case ERROR_TYPES.VALIDATION:
    case ERROR_TYPES.NOT_FOUND:
      return 'low'
    
    case ERROR_TYPES.AUTHENTICATION:
    case ERROR_TYPES.AUTHORIZATION:
    case ERROR_TYPES.DUPLICATE:
      return 'medium'
    
    case ERROR_TYPES.FILE_OPERATION:
    case ERROR_TYPES.NETWORK:
      return 'high'
    
    case ERROR_TYPES.DATABASE:
    case ERROR_TYPES.UNKNOWN:
      return 'critical'
    
    default:
      return 'medium'
  }
}

// 默认导出
export default {
  ERROR_TYPES,
  getErrorType,
  getUserFriendlyMessage,
  handleError,
  createErrorAlert,
  isUserError,
  getErrorSeverity
}
