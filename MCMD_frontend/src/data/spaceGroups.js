export const spaceGroups = [
  {
    category: 'Triclinic',
    groups: [
      {number: 1, name: 'P1'},
      {number: 2, name: 'P-1'}
    ]
  },
  {
    category: 'Monoclinic',
    groups: [
      {number: 3, name: 'P2'},
      {number: 4, name: 'P2_1'},
      {number: 5, name: 'C2'},
      {number: 6, name: 'Pm'},
      {number: 7, name: 'Pc'},
      {number: 8, name: 'Cm'},
      {number: 9, name: 'Cc'},
      {number: 10, name: 'P2/m'},
      {number: 11, name: 'P2_1/m'},
      {number: 12, name: 'C2/m'},
      {number: 13, name: 'P2/c'},
      {number: 14, name: 'P2_1/c'},
      {number: 15, name: 'C2/c'}
    ]
  },
  {
    category: 'Orthorhombic',
    groups: [
      {number: 16, name: 'P222'},
      {number: 17, name: 'P222_1'},
      {number: 18, name: 'P2_12_12'},
      {number: 19, name: 'P2_12_12_1'},
      {number: 20, name: 'C222_1'},
      {number: 21, name: 'C222'},
      {number: 22, name: 'F222'},
      {number: 23, name: 'I222'},
      {number: 24, name: 'I2_12_12_1'},
      {number: 25, name: 'Pmm2'},
      {number: 26, name: 'Pmc2_1'},
      {number: 27, name: 'Pcc2'},
      {number: 28, name: 'Pma2'},
      {number: 29, name: 'Pca2_1'},
      {number: 30, name: 'Pnc2'},
      {number: 31, name: 'Pmn2_1'},
      {number: 32, name: 'Pba2'},
      {number: 33, name: 'Pna2_1'},
      {number: 34, name: 'Pnn2'},
      {number: 35, name: 'Cmm2'},
      {number: 36, name: 'Cmc2_1'},
      {number: 37, name: 'Ccc2'},
      {number: 38, name: 'Amm2'},
      {number: 39, name: 'Aem2'},
      {number: 40, name: 'Ama2'},
      {number: 41, name: 'Aea2'},
      {number: 42, name: 'Fmm2'},
      {number: 43, name: 'Fdd2'},
      {number: 44, name: 'Imm2'},
      {number: 45, name: 'Iba2'},
      {number: 46, name: 'Ima2'},
      {number: 47, name: 'Pmmm'},
      {number: 48, name: 'Pnnn'},
      {number: 49, name: 'Pccm'},
      {number: 50, name: 'Pban'},
      {number: 51, name: 'Pmma'},
      {number: 52, name: 'Pnna'},
      {number: 53, name: 'Pmna'},
      {number: 54, name: 'Pcca'},
      {number: 55, name: 'Pbam'},
      {number: 56, name: 'Pccn'},
      {number: 57, name: 'Pbcm'},
      {number: 58, name: 'Pnnm'},
      {number: 59, name: 'Pmmn'},
      {number: 60, name: 'Pbcn'},
      {number: 61, name: 'Pbca'},
      {number: 62, name: 'Pnma'},
      {number: 63, name: 'Cmcm'},
      {number: 64, name: 'Cmce'},
      {number: 65, name: 'Cmmm'},
      {number: 66, name: 'Cccm'},
      {number: 67, name: 'Cmme'},
      {number: 68, name: 'Ccce'},
      {number: 69, name: 'Fmmm'},
      {number: 70, name: 'Fddd'},
      {number: 71, name: 'Immm'},
      {number: 72, name: 'Ibam'},
      {number: 73, name: 'Ibca'},
      {number: 74, name: 'Imma'}
    ]
  },
  {
    category: 'Tetragonal',
    groups: [
      {number: 75, name: 'P4'},
      {number: 76, name: 'P4_1'},
      {number: 77, name: 'P4_2'},
      {number: 78, name: 'P4_3'},
      {number: 79, name: 'I4'},
      {number: 80, name: 'I4_1'},
      {number: 81, name: 'P-4'},
      {number: 82, name: 'I-4'},
      {number: 83, name: 'P4/m'},
      {number: 84, name: 'P4_2/m'},
      {number: 85, name: 'P4/n'},
      {number: 86, name: 'P4_2/n'},
      {number: 87, name: 'I4/m'},
      {number: 88, name: 'I4_1/a'},
      {number: 89, name: 'P422'},
      {number: 90, name: 'P42_12'},
      {number: 91, name: 'P4_122'},
      {number: 92, name: 'P4_12_12'},
      {number: 93, name: 'P4_222'},
      {number: 94, name: 'P4_22_12'},
      {number: 95, name: 'P4_322'},
      {number: 96, name: 'P4_32_12'},
      {number: 97, name: 'I422'},
      {number: 98, name: 'I4_122'},
      {number: 99, name: 'P4mm'},
      {number: 100, name: 'P4bm'},
      {number: 101, name: 'P4_2cm'},
      {number: 102, name: 'P4_2nm'},
      {number: 103, name: 'P4cc'},
      {number: 104, name: 'P4nc'},
      {number: 105, name: 'P4_2mc'},
      {number: 106, name: 'P4_2bc'},
      {number: 107, name: 'I4mm'},
      {number: 108, name: 'I4cm'},
      {number: 109, name: 'I4_1md'},
      {number: 110, name: 'I4_1cd'},
      {number: 111, name: 'P-42m'},
      {number: 112, name: 'P-42c'},
      {number: 113, name: 'P-42_1m'},
      {number: 114, name: 'P-42_1c'},
      {number: 115, name: 'P-4m2'},
      {number: 116, name: 'P-4c2'},
      {number: 117, name: 'P-4b2'},
      {number: 118, name: 'P-4n2'},
      {number: 119, name: 'I-4m2'},
      {number: 120, name: 'I-4c2'},
      {number: 121, name: 'I-42m'},
      {number: 122, name: 'I-42d'},
      {number: 123, name: 'P4/mmm'},
      {number: 124, name: 'P4/mcc'},
      {number: 125, name: 'P4/nbm'},
      {number: 126, name: 'P4/nnc'},
      {number: 127, name: 'P4/mbm'},
      {number: 128, name: 'P4/mnc'},
      {number: 129, name: 'P4/nmm'},
      {number: 130, name: 'P4/ncc'},
      {number: 131, name: 'P4_2/mmc'},
      {number: 132, name: 'P4_2/mcm'},
      {number: 133, name: 'P4_2/nbc'},
      {number: 134, name: 'P4_2/nnm'},
      {number: 135, name: 'P4_2/mbc'},
      {number: 136, name: 'P4_2/mnm'},
      {number: 137, name: 'P4_2/nmc'},
      {number: 138, name: 'P4_2/ncm'},
      {number: 139, name: 'I4/mmm'},
      {number: 140, name: 'I4/mcm'},
      {number: 141, name: 'I4_1/amd'},
      {number: 142, name: 'I4_1/acd'}
    ]
  },
  {
    category: 'Trigonal',
    groups: [
      {number: 143, name: 'P3'},
      {number: 144, name: 'P3_1'},
      {number: 145, name: 'P3_2'},
      {number: 146, name: 'R3'},
      {number: 147, name: 'P-3'},
      {number: 148, name: 'R-3'},
      {number: 149, name: 'P312'},
      {number: 150, name: 'P321'},
      {number: 151, name: 'P3_112'},
      {number: 152, name: 'P3_121'},
      {number: 153, name: 'P3_212'},
      {number: 154, name: 'P3_221'},
      {number: 155, name: 'R32'},
      {number: 156, name: 'P3m1'},
      {number: 157, name: 'P31m'},
      {number: 158, name: 'P3c1'},
      {number: 159, name: 'P31c'},
      {number: 160, name: 'R3m'},
      {number: 161, name: 'R3c'},
      {number: 162, name: 'P-31m'},
      {number: 163, name: 'P-31c'},
      {number: 164, name: 'P-3m1'},
      {number: 165, name: 'P-3c1'},
      {number: 166, name: 'R-3m'},
      {number: 167, name: 'R-3c'}
    ]
  },
  {
    category: 'Hexagonal',
    groups: [
      {number: 168, name: 'P6'},
      {number: 169, name: 'P6_1'},
      {number: 170, name: 'P6_5'},
      {number: 171, name: 'P6_2'},
      {number: 172, name: 'P6_4'},
      {number: 173, name: 'P6_3'},
      {number: 174, name: 'P-6'},
      {number: 175, name: 'P6/m'},
      {number: 176, name: 'P6_3/m'},
      {number: 177, name: 'P622'},
      {number: 178, name: 'P6_122'},
      {number: 179, name: 'P6_522'},
      {number: 180, name: 'P6_222'},
      {number: 181, name: 'P6_422'},
      {number: 182, name: 'P6_322'},
      {number: 183, name: 'P6mm'},
      {number: 184, name: 'P6cc'},
      {number: 185, name: 'P6_3cm'},
      {number: 186, name: 'P6_3mc'},
      {number: 187, name: 'P-6m2'},
      {number: 188, name: 'P-6c2'},
      {number: 189, name: 'P-62m'},
      {number: 190, name: 'P-62c'},
      {number: 191, name: 'P6/mmm'},
      {number: 192, name: 'P6/mcc'},
      {number: 193, name: 'P6_3/mcm'},
      {number: 194, name: 'P6_3/mmc'}
    ]
  },
  {
    category: 'Cubic',
    groups: [
      {number: 195, name: 'P23'},
      {number: 196, name: 'F23'},
      {number: 197, name: 'I23'},
      {number: 198, name: 'P2_13'},
      {number: 199, name: 'I2_13'},
      {number: 200, name: 'Pm-3'},
      {number: 201, name: 'Pn-3'},
      {number: 202, name: 'Fm-3'},
      {number: 203, name: 'Fd-3'},
      {number: 204, name: 'Im-3'},
      {number: 205, name: 'Pa-3'},
      {number: 206, name: 'Ia-3'},
      {number: 207, name: 'P432'},
      {number: 208, name: 'P4_232'},
      {number: 209, name: 'F432'},
      {number: 210, name: 'F4_132'},
      {number: 211, name: 'I432'},
      {number: 212, name: 'P4_332'},
      {number: 213, name: 'P4_132'},
      {number: 214, name: 'I4_132'},
      {number: 215, name: 'P-43m'},
      {number: 216, name: 'F-43m'},
      {number: 217, name: 'I-43m'},
      {number: 218, name: 'P-43n'},
      {number: 219, name: 'F-43c'},
      {number: 220, name: 'I-43d'},
      {number: 221, name: 'Pm-3m'},
      {number: 222, name: 'Pn-3n'},
      {number: 223, name: 'Pm-3n'},
      {number: 224, name: 'Pn-3m'},
      {number: 225, name: 'Fm-3m'},
      {number: 226, name: 'Fm-3c'},
      {number: 227, name: 'Fd-3m'},
      {number: 228, name: 'Fd-3c'},
      {number: 229, name: 'Im-3m'},
      {number: 230, name: 'Ia-3d'}
    ]
  }
] 