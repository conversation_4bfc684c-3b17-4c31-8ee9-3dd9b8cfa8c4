<template>
  <v-snackbar
    v-model="show"
    :color="color"
    :timeout="timeout"
    location="top"
    class="text-center custom-snackbar"
    rounded="pill"
  >
    <div class="d-flex align-center">
      <v-icon
        :icon="icon"
        class="mr-3"
        size="24"
      ></v-icon>
      <span class="text-subtitle-1 font-weight-medium letter-spacing-1">{{ message }}</span>
    </div>

    <template v-slot:actions>
      <v-btn
        variant="text"
        icon="mdi-close"
        @click="close"
        class="ml-3"
      ></v-btn>
    </template>
  </v-snackbar>
</template>

<script>
export default {
  name: 'UnifiedSnackbar',
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: 'success'
    },
    timeout: {
      type: Number,
      default: 3000
    }
  },
  
  computed: {
    show: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    
    icon() {
      switch (this.color) {
        case 'success':
          return 'mdi-check-circle'
        case 'error':
          return 'mdi-alert-circle'
        case 'warning':
          return 'mdi-alert'
        case 'info':
          return 'mdi-information'
        default:
          return 'mdi-check-circle'
      }
    }
  },
  
  methods: {
    close() {
      this.show = false
    }
  }
}
</script>

<style scoped>
/* 提示框样式 */
.custom-snackbar {
  margin-top: 20px !important;
}

:deep(.custom-snackbar .v-snackbar__wrapper) {
  min-width: 300px !important;
  padding: 16px 20px !important;
  border-radius: 12px !important;
}

:deep(.custom-snackbar .v-snackbar__content) {
  padding: 0 !important;
}

.letter-spacing-1 {
  letter-spacing: 0.5px !important;
}

:deep(.custom-snackbar.v-snackbar--color-success .v-snackbar__wrapper) {
  background: linear-gradient(45deg, #4caf50, #66bb6a) !important;
}

:deep(.custom-snackbar.v-snackbar--color-error .v-snackbar__wrapper) {
  background: linear-gradient(45deg, #f44336, #ef5350) !important;
}

:deep(.custom-snackbar.v-snackbar--color-warning .v-snackbar__wrapper) {
  background: linear-gradient(45deg, #ff9800, #ffb74d) !important;
}

:deep(.custom-snackbar.v-snackbar--color-info .v-snackbar__wrapper) {
  background: linear-gradient(45deg, #2196f3, #42a5f5) !important;
}
</style>
