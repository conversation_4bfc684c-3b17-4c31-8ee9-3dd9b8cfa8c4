<template>
  <v-dialog v-model="show" max-width="500" :persistent="persistent">
    <v-card>
      <v-card-title class="text-h5 pa-6 d-flex align-center">
        <v-icon :color="iconColor" class="mr-2" size="28">{{ icon }}</v-icon>
        {{ title }}
        <v-spacer></v-spacer>
        <v-chip 
          v-if="showAdminChip"
          color="orange" 
          variant="outlined" 
          size="small" 
          prepend-icon="mdi-shield-account"
        >
          管理员操作
        </v-chip>
      </v-card-title>
      
      <v-card-text class="pa-6">
        <!-- 警告提示 -->
        <v-alert
          :type="alertType"
          variant="tonal"
          border="start"
          :icon="alertIcon"
          class="mb-4"
        >
          <strong>{{ alertTitle }}</strong><br>
          <div v-html="alertMessage"></div>
        </v-alert>
        
        <!-- 详细内容 -->
        <div v-if="detailContent">
          <p>{{ message }}</p>
          <v-card variant="tonal" color="info" class="mt-4">
            <v-card-text class="pa-4">
              <div class="d-flex align-start">
                <v-icon color="info" class="mr-3 mt-1" size="20">mdi-information-outline</v-icon>
                <div class="flex-grow-1">
                  <div class="text-subtitle-2 font-weight-medium mb-2">操作详情</div>
                  <div class="text-body-2 line-height-relaxed">
                    <slot name="detail-content">
                      <div v-for="(line, index) in formatDetailContentArray(detailContent)" :key="index" class="detail-content-item">
                        <v-icon v-if="line.isBullet" class="mr-3 mt-1" size="10" color="info">mdi-circle</v-icon>
                        <span v-html="line.text"></span>
                      </div>
                    </slot>
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </div>
        
        <!-- 简单消息 -->
        <p v-else>{{ message }}</p>
      </v-card-text>
      
      <v-card-actions class="pa-6">
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          @click="cancel"
          :disabled="isLoading"
        >
          取消
        </v-btn>
        <v-btn
          :color="confirmButtonColor"
          @click="confirm"
          :loading="isLoading"
          :disabled="isLoading"
        >
          {{ confirmText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'UnifiedConfirmDialog',
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'warning', // 'warning', 'error', 'info'
      validator: value => ['warning', 'error', 'info'].includes(value)
    },
    confirmText: {
      type: String,
      default: '确认'
    },
    loading: {
      type: Boolean,
      default: false
    },
    confirmAction: {
      type: Function,
      default: null
    },
    showAdminChip: {
      type: Boolean,
      default: false
    },
    detailContent: {
      type: String,
      default: null
    },
    customAlertMessage: {
      type: String,
      default: null
    },
    persistent: {
      type: Boolean,
      default: false  // 默认允许点击空白处关闭
    }
  },
  
  computed: {
    show: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    
    icon() {
      switch (this.type) {
        case 'error':
          return 'mdi-alert-circle'
        case 'warning':
          return 'mdi-alert'
        case 'info':
          return 'mdi-information'
        default:
          return 'mdi-alert'
      }
    },
    
    iconColor() {
      switch (this.type) {
        case 'error':
          return 'error'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'warning'
      }
    },
    
    alertType() {
      return this.type === 'warning' ? 'warning' : this.type
    },
    
    alertIcon() {
      return this.icon
    },
    
    alertTitle() {
      switch (this.type) {
        case 'error':
          return '危险操作警告'
        case 'warning':
          return '警告'
        case 'info':
          return '提示'
        default:
          return '警告'
      }
    },
    
    alertMessage() {
      // 如果有自定义消息，使用自定义消息
      if (this.customAlertMessage) {
        return this.customAlertMessage
      }

      // 默认消息
      switch (this.type) {
        case 'error':
          return '此操作具有风险且不可撤销，请确认是否继续？'
        case 'warning':
          return '此操作不可撤销，请确认是否继续？'
        case 'info':
          return '请确认是否执行此操作？'
        default:
          return '此操作不可撤销，请确认是否继续？'
      }
    },
    
    confirmButtonColor() {
      switch (this.type) {
        case 'error':
          return 'error'
        case 'warning':
          return 'warning'
        case 'info':
          return 'primary'
        default:
          return 'warning'
      }
    },

    isLoading() {
      return this.loading || this.internalLoading
    }
  },
  
  data() {
    return {
      internalLoading: false
    }
  },

  methods: {
    async confirm() {
      if (this.confirmAction) {
        // 如果有confirmAction函数，执行它并等待完成
        this.internalLoading = true
        try {
          await this.confirmAction()
          // 操作成功后自动关闭对话框
          this.show = false
        } catch (error) {
          // 如果操作失败，不关闭对话框，让用户可以重试或取消
          console.error('确认操作失败:', error)
        } finally {
          this.internalLoading = false
        }
      } else {
        // 如果没有confirmAction，使用传统的事件方式
        this.$emit('confirm')
        // 注意：传统方式不自动关闭对话框，由父组件决定
      }
    },

    cancel() {
      this.show = false
      this.$emit('cancel')
    },

    formatDetailContentArray(content) {
      if (!content) return []

      // 将内容分割成行，并标记哪些是列表项
      return content
        .split('\n')
        .map(line => {
          const trimmed = line.trim()
          if (trimmed.startsWith('•')) {
            return {
              text: trimmed.substring(1).trim(),
              isBullet: true
            }
          }
          return trimmed ? {
            text: trimmed,
            isBullet: false
          } : null
        })
        .filter(item => item !== null)
    }
  }
}
</script>

<style scoped>
.line-height-relaxed {
  line-height: 1.6;
}

/* 确保详细内容区域的图标对齐 */
.detail-content-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detail-content-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  min-height: 20px;
}

.detail-content-item:last-child {
  margin-bottom: 0;
}

/* 确保图标和文本对齐 */
.detail-content-item .v-icon {
  flex-shrink: 0;
}
</style>
