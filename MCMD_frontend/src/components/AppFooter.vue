<template>
  <v-footer class="app-footer bg-grey-lighten-4 mt-8">
    <v-container>
      <v-row align="center" justify="center">
        <v-col cols="12" class="text-center">
          <!-- 备案信息 -->
          <div class="footer-content">
            <p class="text-body-2 mb-2 text-grey-darken-1">
              © {{ currentYear }} MCMD - Magnetocaloric Materials Database
            </p>
            <p class="text-body-2 mb-2">
              <a 
                href="https://beian.miit.gov.cn" 
                target="_blank" 
                class="beian-link text-decoration-none"
                rel="noopener noreferrer"
              >
                京ICP备2025134439号-1
              </a>
            </p>
            <p class="text-caption text-grey-darken-1 mb-0">
              Theoretical Condensed Matter Physics & Computational Materials Physics Laboratory
            </p>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-footer>
</template>

<script>
export default {
  name: 'AppFooter',
  computed: {
    currentYear() {
      return new Date().getFullYear()
    }
  }
}
</script>

<style scoped>
.app-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  margin-top: auto;
}

.footer-content {
  padding: 16px 0;
}

.beian-link {
  color: #666 !important;
  transition: color 0.3s ease;
}

.beian-link:hover {
  color: #1976d2 !important;
  text-decoration: underline !important;
}

/* 确保在小屏幕上也能正常显示 */
@media (max-width: 600px) {
  .footer-content {
    padding: 12px 0;
  }
  
  .text-body-2 {
    font-size: 0.875rem !important;
  }
  
  .text-caption {
    font-size: 0.75rem !important;
  }
}
</style>
