<template>
  <!-- 修改密码对话框 -->
  <v-dialog v-model="dialog" max-width="500px">
    <v-card>
      <v-card-title class="text-h5 pa-6 bg-primary">
        <v-icon color="white" class="mr-2">mdi-key-variant</v-icon>
        <span class="white--text">修改密码</span>
      </v-card-title>

      <v-card-text class="pa-6">
        <v-form ref="changePasswordForm" v-model="formValid">
          <v-text-field
            v-model="passwordForm.currentPassword"
            label="当前密码"
            :type="showCurrentPassword ? 'text' : 'password'"
            :append-inner-icon="showCurrentPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="showCurrentPassword = !showCurrentPassword"
            prepend-inner-icon="mdi-lock"
            variant="outlined"
            density="comfortable"
            :rules="[v => !!v || '请输入当前密码']"
            required
            class="mb-4"
          ></v-text-field>

          <v-text-field
            v-model="passwordForm.newPassword"
            label="新密码"
            :type="showNewPassword ? 'text' : 'password'"
            :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="showNewPassword = !showNewPassword"
            prepend-inner-icon="mdi-lock-plus"
            variant="outlined"
            density="comfortable"
            :rules="passwordRules"
            required
            class="mb-4"
          ></v-text-field>

          <v-text-field
            v-model="passwordForm.confirmPassword"
            label="确认新密码"
            :type="showConfirmPassword ? 'text' : 'password'"
            :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="showConfirmPassword = !showConfirmPassword"
            prepend-inner-icon="mdi-lock-check"
            variant="outlined"
            density="comfortable"
            :rules="confirmPasswordRules"
            required
          ></v-text-field>

          <v-alert
            v-if="passwordError"
            type="error"
            variant="tonal"
            class="mt-4"
            closable
            @click:close="passwordError = ''"
          >
            {{ passwordError }}
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer></v-spacer>
        <v-btn
          color="grey"
          variant="outlined"
          @click="closeDialog"
          :disabled="loading"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="changePassword"
          :loading="loading"
          :disabled="!formValid"
        >
          修改密码
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { userApi } from '@/utils/api'

export default {
  name: 'ChangePasswordDialog',
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:modelValue', 'success', 'error'],

  data() {
    return {
      formValid: false,
      loading: false,
      showCurrentPassword: false,
      showNewPassword: false,
      showConfirmPassword: false,
      passwordError: '',
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },

      // 密码验证规则
      passwordRules: [
        v => !!v || '密码不能为空',
        v => (v && v.length >= 6) || '密码至少6个字符'
      ]
    }
  },

  computed: {
    dialog: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },

    confirmPasswordRules() {
      return [
        v => !!v || '请确认新密码',
        v => v === this.passwordForm.newPassword || '两次输入的密码不一致'
      ]
    }
  },

  watch: {
    modelValue(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },

  methods: {
    closeDialog() {
      this.dialog = false
      this.resetForm()
    },

    resetForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.passwordError = ''
      this.showCurrentPassword = false
      this.showNewPassword = false
      this.showConfirmPassword = false
      if (this.$refs.changePasswordForm) {
        this.$refs.changePasswordForm.resetValidation()
      }
    },

    async changePassword() {
      if (!this.formValid) return

      this.loading = true
      this.passwordError = ''

      try {
        await userApi.changePassword({
          currentPassword: this.passwordForm.currentPassword,
          newPassword: this.passwordForm.newPassword
        })

        this.$emit('success', '密码修改成功，请重新登录')
        this.closeDialog()

        // 修改密码后自动登出，要求重新登录
        setTimeout(() => {
          this.$router.push('/login')
        }, 2000)

      } catch (error) {
        console.error('修改密码失败:', error)
        this.passwordError = error.response?.data?.error || '密码修改失败，请检查当前密码是否正确'
        this.$emit('error', this.passwordError)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
