<template>
    <v-alert
      v-if="message"
      type="error"
      closable
      class="mb-4"
      @click:close="closeAlert"
    >
      {{ message }}
    </v-alert>
  </template>
  
  <script>
  export default {
    name: 'ErrorAlert',
    
    props: {
      message: {
        type: String,
        default: null
      }
    },
    
    methods: {
      closeAlert() {
        this.$emit('update:message', null);
      }
    }
  }
  </script>
  