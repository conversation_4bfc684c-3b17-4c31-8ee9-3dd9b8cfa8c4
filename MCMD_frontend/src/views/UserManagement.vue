<template>
  <v-container fluid class="user-management-page pa-6">
    <div class="content-wrapper">
      <v-row>
        <v-col cols="12">
          <v-card class="mb-6" elevation="0">
            <v-card-title class="d-flex align-center py-4 px-6 bg-gradient">
              <v-icon color="white" class="mr-2" size="28">mdi-account-group</v-icon>
              <span class="text-h5 font-weight-bold white--text">用户管理系统</span>
              <v-spacer></v-spacer>
              <!-- 用户信息显示 -->
              <div class="d-flex align-center">
                <v-chip
                  color="white"
                  variant="flat"
                  size="small"
                  prepend-icon="mdi-account"
                  class="text-black"
                >
                  <span class="font-weight-medium">{{ currentUser || '未登录' }}</span>
                </v-chip>
                <v-chip
                  color="white"
                  variant="outlined"
                  size="small"
                  prepend-icon="mdi-shield-account"
                  class="text-white ml-3"
                  style="border-color: white;"
                >
                  管理员
                </v-chip>
              </div>
            </v-card-title>

            <!-- 统计信息和操作按钮 -->
            <v-card-text class="pa-6">
              <!-- 用户统计卡片 -->
              <v-row class="mb-6">
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="primary" variant="tonal">
                    <v-icon size="48" color="primary" class="mb-2">mdi-account-multiple</v-icon>
                    <div class="text-h4 font-weight-bold">{{ totalUsers }}</div>
                    <div class="text-subtitle-2">总用户数</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="success" variant="tonal">
                    <v-icon size="48" color="success" class="mb-2">mdi-account-check</v-icon>
                    <div class="text-h4 font-weight-bold">{{ activeUsers }}</div>
                    <div class="text-subtitle-2">活跃用户</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="info" variant="tonal">
                    <v-icon size="48" color="info" class="mb-2">mdi-shield-account</v-icon>
                    <div class="text-h4 font-weight-bold">{{ adminUsers }}</div>
                    <div class="text-subtitle-2">管理员</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="error" variant="tonal">
                    <v-icon size="48" color="error" class="mb-2">mdi-account-cancel</v-icon>
                    <div class="text-h4 font-weight-bold">{{ bannedUsers }}</div>
                    <div class="text-subtitle-2">已禁用用户</div>
                  </v-card>
                </v-col>
              </v-row>

              <!-- 用户列表标题和操作 -->
              <div class="d-flex align-center justify-space-between mb-4">
                <h3 class="text-h6 font-weight-bold">用户列表</h3>
                <div class="d-flex align-center">
                  <v-btn
                    color="success"
                    prepend-icon="mdi-account-plus"
                    @click="openAddUserDialog"
                    variant="elevated"
                    class="operation-btn mr-4"
                  >
                    新增用户
                  </v-btn>
                  <v-btn
                    color="primary"
                    prepend-icon="mdi-refresh"
                    @click="loadUsers"
                    :loading="loading"
                    variant="outlined"
                    class="operation-btn"
                  >
                    刷新数据
                  </v-btn>
                </div>
              </div>
          <v-data-table
            :headers="headers"
            :items="users"
            :loading="loading"
            class="elevation-0"
            item-key="username"
          >
            <!-- 角色列 -->
            <template v-slot:item.role="{ item }">
              <v-chip
                :color="getRoleColor(item.role)"
                size="small"
                variant="tonal"
              >
                <v-icon start :icon="getRoleIcon(item.role)"></v-icon>
                {{ getRoleText(item.role) }}
              </v-chip>
            </template>

            <!-- 状态列 -->
            <template v-slot:item.status="{ item }">
              <v-chip
                :color="getStatusColor(item.status)"
                size="small"
                variant="tonal"
              >
                <v-icon start :icon="getStatusIcon(item.status)"></v-icon>
                {{ getStatusText(item.status) }}
              </v-chip>
            </template>

            <!-- 创建时间列 -->
            <template v-slot:item.createdAt="{ item }">
              {{ formatDate(item.createdAt) }}
            </template>

            <!-- 最后登录时间列 -->
            <template v-slot:item.lastLoginAt="{ item }">
              {{ item.lastLoginAt ? formatDate(item.lastLoginAt) : '从未登录' }}
            </template>

            <!-- 操作列 -->
            <template v-slot:item.actions="{ item }">
              <div class="d-flex gap-2 align-center">
                <!-- 编辑用户信息 - 可以编辑自己或普通用户的信息 -->
                <v-btn
                  v-if="canEditUser(item)"
                  icon="mdi-pencil"
                  size="small"
                  color="primary"
                  variant="text"
                  @click="editUser(item)"
                  :title="'编辑用户信息'"
                ></v-btn>

                <!-- 普通用户：显示其他操作 -->
                <template v-if="canOperateUser(item)">

                  <!-- 角色管理 -->
                  <v-menu>
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-shield-account"
                        size="small"
                        color="info"
                        variant="text"
                        v-bind="props"
                        :title="'角色管理'"
                      ></v-btn>
                    </template>
                    <v-list>
                      <v-list-item @click="promoteToAdmin(item)">
                        <template v-slot:prepend>
                          <v-icon icon="mdi-arrow-up" color="success"></v-icon>
                        </template>
                        <v-list-item-title>提升为管理员</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>

                  <!-- 状态管理 -->
                  <v-btn
                    :icon="item.status === 'active' ? 'mdi-account-cancel' : 'mdi-account-check'"
                    size="small"
                    :color="item.status === 'active' ? 'warning' : 'success'"
                    variant="text"
                    @click="toggleUserStatus(item)"
                    :title="item.status === 'active' ? '禁用用户' : '启用用户'"
                  ></v-btn>

                  <!-- 重置密码 -->
                  <v-btn
                    icon="mdi-key-variant"
                    size="small"
                    color="secondary"
                    variant="text"
                    @click="resetPassword(item)"
                    :title="'重置密码'"
                  ></v-btn>
                </template>

                <!-- 当前用户：显示自己标识 -->
                <template v-else-if="item.username === currentUser">
                  <v-chip color="primary" size="small" variant="tonal">
                    <v-icon start>mdi-account-star</v-icon>
                    当前用户
                  </v-chip>
                </template>

                <!-- 其他管理员：显示保护标识 -->
                <template v-else-if="item.role === 'admin'">
                  <v-chip color="success" size="small" variant="tonal">
                    <v-icon start>mdi-shield-account</v-icon>
                    管理员
                  </v-chip>
                  <span class="text-caption text-medium-emphasis ml-2">受保护用户</span>
                </template>
              </div>
            </template>
          </v-data-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <!-- 新增用户对话框 -->
    <v-dialog v-model="addUserDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title class="text-h5 pa-6 bg-primary">
          <v-icon color="white" class="mr-2">mdi-account-plus</v-icon>
          <span class="white--text">新增用户</span>
        </v-card-title>

        <v-card-text class="pa-6">
          <v-form ref="addUserForm" v-model="addUserFormValid">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="newUser.username"
                  label="用户名 *"
                  :rules="usernameRules"
                  required
                  prepend-inner-icon="mdi-account"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="newUser.email"
                  label="邮箱 *"
                  :rules="emailRules"
                  required
                  prepend-inner-icon="mdi-email"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="newUser.password"
                  label="密码 *"
                  :rules="passwordRules"
                  required
                  :type="showNewUserPassword ? 'text' : 'password'"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showNewUserPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showNewUserPassword = !showNewUserPassword"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="newUser.role"
                  label="用户角色"
                  :items="availableRoles"
                  item-title="text"
                  item-value="value"
                  required
                  prepend-inner-icon="mdi-shield-account"
                  variant="outlined"
                  density="comfortable"
                ></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="newUser.realName"
                  label="真实姓名"
                  prepend-inner-icon="mdi-card-account-details"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="newUser.organization"
                  label="组织机构"
                  prepend-inner-icon="mdi-domain"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="outlined"
            @click="closeAddUserDialog"
            :disabled="addUserLoading"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="addUser"
            :loading="addUserLoading"
            :disabled="!addUserFormValid"
          >
            创建用户
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 编辑用户对话框 -->
    <v-dialog v-model="editUserDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title class="text-h5 pa-6 bg-primary">
          <v-icon color="white" class="mr-2">mdi-account-edit</v-icon>
          <span class="white--text">编辑用户信息</span>
        </v-card-title>

        <v-card-text class="pa-6">
          <v-form ref="editUserForm" v-model="editUserFormValid">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="editingUser.username"
                  label="用户名"
                  prepend-inner-icon="mdi-account"
                  variant="outlined"
                  density="comfortable"
                  readonly
                  hint="用户名不可修改"
                  persistent-hint
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="editingUser.email"
                  label="邮箱"
                  :rules="emailRules"
                  prepend-inner-icon="mdi-email"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="editingUser.realName"
                  label="真实姓名"
                  prepend-inner-icon="mdi-card-account-details"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="editingUser.organization"
                  label="组织机构"
                  prepend-inner-icon="mdi-domain"
                  variant="outlined"
                  density="comfortable"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="outlined"
            @click="closeEditDialog"
            :disabled="editUserLoading"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="saveUserChanges"
            :loading="editUserLoading"
            :disabled="!editUserFormValid"
          >
            保存更改
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 重置密码结果对话框 -->
    <v-dialog v-model="resetPasswordDialog" max-width="500px" persistent>
      <v-card>
        <v-card-title class="text-h5 pa-6 bg-warning">
          <v-icon color="white" class="mr-2">mdi-key-variant</v-icon>
          <span class="white--text">密码重置成功</span>
        </v-card-title>

        <v-card-text class="pa-6">
          <v-alert
            type="success"
            variant="tonal"
            class="mb-4"
            prominent
          >
            <template v-slot:prepend>
              <v-icon>mdi-check-circle</v-icon>
            </template>
            <div>
              <div class="text-h6 mb-2">密码重置完成</div>
              <p class="mb-2">用户 <strong>{{ newPasswordInfo.username }}</strong> 的密码已成功重置。</p>
            </div>
          </v-alert>

          <v-card variant="outlined" class="pa-4 mb-4">
            <div class="text-subtitle-1 mb-2">
              <v-icon color="warning" class="mr-2">mdi-alert</v-icon>
              新的临时密码
            </div>
            <v-text-field
              :value="newPasswordInfo.newPassword"
              label="临时密码"
              variant="outlined"
              density="comfortable"
              readonly
              append-inner-icon="mdi-content-copy"
              @click:append-inner="copyPassword"
              class="password-field"
            ></v-text-field>
            <div class="text-caption text-medium-emphasis">
              请将此密码安全地提供给用户，并要求用户首次登录后立即修改密码。
            </div>
          </v-card>

          <v-alert
            type="warning"
            variant="tonal"
            density="compact"
          >
            <v-icon start>mdi-security</v-icon>
            出于安全考虑，请确保通过安全渠道将密码传达给用户。
          </v-alert>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            variant="elevated"
            @click="closeResetPasswordDialog"
          >
            我已记录密码
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>



    <!-- 确认对话框 -->
    <UnifiedConfirmDialog
      v-model="confirmDialog"
      :title="confirmTitle"
      :message="confirmMessage"
      :type="confirmType"
      :confirm-text="confirmButtonText"
      :custom-alert-message="confirmAlertMessage"
      :show-admin-chip="true"
      :confirm-action="confirmAction"
      @cancel="confirmDialog = false"
    />

    <!-- 消息提示 -->
    <UnifiedSnackbar
      v-model="snackbar"
      :message="snackbarMessage"
      :color="snackbarColor"
    />
  </v-container>
</template>

<script>
import { userApi, authApi } from '@/utils/api'
import UnifiedSnackbar from '@/components/UnifiedSnackbar.vue'
import UnifiedConfirmDialog from '@/components/UnifiedConfirmDialog.vue'

export default {
  name: 'UserManagement',

  components: {
    UnifiedSnackbar,
    UnifiedConfirmDialog
  },

  data() {
    return {
      users: [],
      loading: false,
      currentUser: '',
      confirmDialog: false,
      confirmTitle: '确认操作',
      confirmMessage: '',
      confirmType: 'warning',
      confirmButtonText: '确认',
      confirmAlertMessage: '',
      confirmAction: null,
      snackbar: false,
      snackbarMessage: '',
      snackbarColor: 'success',
      
      headers: [
        { title: '用户名', key: 'username', sortable: true },
        { title: '邮箱', key: 'email', sortable: true },
        { title: '姓名', key: 'realName', sortable: true },
        { title: '机构', key: 'organization', sortable: true },
        { title: '角色', key: 'role', sortable: true },
        { title: '状态', key: 'status', sortable: true },
        { title: '创建时间', key: 'createdAt', sortable: true },
        { title: '最后登录', key: 'lastLoginAt', sortable: true },
        { title: '操作', key: 'actions', sortable: false }
      ],

      availableRoles: [
        { value: 'user', text: '普通用户', icon: 'mdi-account', color: 'info' },
        { value: 'admin', text: '管理员', icon: 'mdi-shield-account', color: 'success' }
      ],

      availableStatuses: [
        { value: 'active', text: '激活', icon: 'mdi-account-check', color: 'success' },
        { value: 'banned', text: '已禁用', icon: 'mdi-account-cancel', color: 'error' }
      ],

      // 新增用户相关数据
      addUserDialog: false,
      addUserFormValid: false,
      addUserLoading: false,
      showNewUserPassword: false,
      newUser: {
        username: '',
        email: '',
        password: '',
        role: 'user',
        realName: '',
        organization: ''
      },

      // 表单验证规则
      usernameRules: [
        v => !!v || '用户名不能为空',
        v => (v && v.length >= 3) || '用户名至少3个字符',
        v => (v && v.length <= 20) || '用户名不能超过20个字符',
        v => /^[a-zA-Z0-9_]+$/.test(v) || '用户名只能包含字母、数字和下划线'
      ],
      emailRules: [
        v => !!v || '邮箱不能为空',
        v => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址'
      ],
      passwordRules: [
        v => !!v || '密码不能为空',
        v => (v && v.length >= 6) || '密码至少6个字符'
      ],



      // 编辑用户相关数据
      editUserDialog: false,
      editUserFormValid: false,
      editUserLoading: false,
      editingUser: {
        username: '',
        email: '',
        realName: '',
        organization: ''
      },

      // 重置密码相关数据
      resetPasswordDialog: false,
      resetPasswordUser: null,
      newPasswordInfo: {
        username: '',
        newPassword: ''
      }
    }
  },

  computed: {
    totalUsers() {
      return this.users.length
    },

    activeUsers() {
      return this.users.filter(user => user.status === 'active').length
    },

    adminUsers() {
      return this.users.filter(user => user.role === 'admin').length
    },

    bannedUsers() {
      return this.users.filter(user => user.status === 'banned').length
    }
  },

  async created() {
    await this.getCurrentUser()
    await this.loadUsers()
  },

  beforeUnmount() {
    // 清理事件监听器
    window.removeEventListener('open-change-password-dialog', this.openChangePasswordDialog)
  },

  methods: {
    // 权限检查函数
    // 检查是否可以编辑用户基本信息（邮箱、姓名、机构）
    canEditUser(user) {
      // 可以编辑自己的基本信息
      if (user.username === this.currentUser) return true
      // 管理员可以编辑普通用户的信息
      return user.role === 'user' && user.username !== this.currentUser
    },

    // 检查是否可以修改用户角色
    canChangeRole(user) {
      return user.role === 'user' && user.username !== this.currentUser
    },

    // 检查是否可以禁用/启用用户
    canToggleStatus(user) {
      return user.role === 'user' && user.username !== this.currentUser
    },

    // 检查是否可以重置密码
    canResetPassword(user) {
      return user.role === 'user' && user.username !== this.currentUser
    },

    // 检查是否可以操作用户（通用检查）
    canOperateUser(user) {
      // 不能操作自己
      if (user.username === this.currentUser) return false
      // 不能操作其他管理员
      if (user.role === 'admin') return false
      // 只能操作普通用户
      return user.role === 'user'
    },

    // 获取操作提示文本
    getOperationHint(user) {
      if (user.username === this.currentUser) {
        return '不能操作自己的账户'
      }
      if (user.role === 'admin') {
        return '管理员账户受保护，无法操作'
      }
      return ''
    },

    // 获取用户可用的角色选项（防止管理员降级其他管理员）
    getAvailableRolesForUser(user) {
      // 如果目标用户是管理员，则不允许降级
      if (user.role === 'admin') {
        return []
      }

      // 对于非管理员用户，返回所有角色选项
      return this.availableRoles
    },

    async getCurrentUser() {
      try {
        const response = await authApi.getCurrentUserInfo()
        this.currentUser = response.username
      } catch (error) {
        console.error('获取当前用户信息失败:', error)
      }
    },

    async loadUsers() {
      this.loading = true
      try {
        const response = await userApi.getAllUsers()
        // 对用户列表进行排序：管理员在前，然后按用户名排序
        this.users = (response.users || []).sort((a, b) => {
          // 首先按角色排序：admin 在前
          if (a.role === 'admin' && b.role !== 'admin') return -1
          if (a.role !== 'admin' && b.role === 'admin') return 1
          // 如果角色相同，按用户名排序
          return a.username.localeCompare(b.username)
        })
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.showMessage('加载用户列表失败', 'error')
      } finally {
        this.loading = false
      }
    },





    getRoleText(role) {
      const roleMap = {
        'user': '普通用户',
        'admin': '管理员'
      }
      return roleMap[role] || role
    },

    getRoleColor(role) {
      const colorMap = {
        'user': 'info',
        'admin': 'success'
      }
      return colorMap[role] || 'default'
    },

    getRoleIcon(role) {
      const iconMap = {
        'user': 'mdi-account',
        'admin': 'mdi-shield-account'
      }
      return iconMap[role] || 'mdi-account'
    },

    getStatusText(status) {
      const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'banned': '已禁用'
      }
      return statusMap[status] || status
    },

    getStatusColor(status) {
      const colorMap = {
        'active': 'success',
        'inactive': 'warning',
        'banned': 'error'
      }
      return colorMap[status] || 'default'
    },

    getStatusIcon(status) {
      const iconMap = {
        'active': 'mdi-account-check',
        'inactive': 'mdi-account-clock',
        'banned': 'mdi-account-cancel'
      }
      return iconMap[status] || 'mdi-account'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    // 新增用户相关方法
    openAddUserDialog() {
      this.addUserDialog = true
      this.resetNewUserForm()
    },

    closeAddUserDialog() {
      this.addUserDialog = false
      this.resetNewUserForm()
    },

    resetNewUserForm() {
      this.newUser = {
        username: '',
        email: '',
        password: '',
        role: 'user',
        realName: '',
        organization: ''
      }
      this.showNewUserPassword = false
      if (this.$refs.addUserForm) {
        this.$refs.addUserForm.resetValidation()
      }
    },

    async addUser() {
      if (!this.addUserFormValid) return

      this.addUserLoading = true
      try {
        await userApi.createUser(this.newUser)
        this.showMessage('用户创建成功', 'success')
        this.closeAddUserDialog()
        await this.loadUsers()
      } catch (error) {
        console.error('创建用户失败:', error)
        this.showMessage(error.response?.data?.error || '创建用户失败', 'error')
      } finally {
        this.addUserLoading = false
      }
    },

    // 编辑用户信息
    editUser(user) {
      this.editingUser = {
        username: user.username,
        email: user.email || '',
        realName: user.realName || '',
        organization: user.organization || ''
      }
      this.editUserDialog = true
    },

    // 关闭编辑对话框
    closeEditDialog() {
      this.editUserDialog = false
      this.resetEditForm()
    },

    // 重置编辑表单
    resetEditForm() {
      this.editingUser = {
        username: '',
        email: '',
        realName: '',
        organization: ''
      }
      if (this.$refs.editUserForm) {
        this.$refs.editUserForm.resetValidation()
      }
    },

    // 保存用户更改
    async saveUserChanges() {
      if (!this.editUserFormValid) return

      this.editUserLoading = true
      try {
        await userApi.updateUser(this.editingUser.username, {
          email: this.editingUser.email,
          realName: this.editingUser.realName,
          organization: this.editingUser.organization
        })

        this.showMessage('用户信息更新成功', 'success')
        this.closeEditDialog()
        await this.loadUsers()
      } catch (error) {
        console.error('更新用户信息失败:', error)
        this.showMessage(error.response?.data?.error || '更新用户信息失败', 'error')
      } finally {
        this.editUserLoading = false
      }
    },

    // 提升为管理员
    promoteToAdmin(user) {
      this.confirmTitle = '提升用户权限'
      this.confirmMessage = `确定要将用户 "${user.username}" 提升为管理员吗？`
      this.confirmType = 'warning'
      this.confirmButtonText = '确认提升'
      this.confirmAlertMessage = `
        <strong>权限变更详情：</strong><br>
        • 当前角色：普通用户<br>
        • 新角色：管理员<br>
        • 新增权限：用户管理、数据管理、系统配置<br>
        <br>
        <strong>⚠️ 重要提醒：</strong><br>
        • 此操作不可撤销<br>
        • 该用户将能够管理其他普通用户<br>
        • 请确保该用户值得信任
      `
      this.confirmAction = () => this.executeRoleChange(user.username, 'admin')
      this.confirmDialog = true
    },

    // 执行角色变更
    async executeRoleChange(username, newRole) {
      await userApi.updateUserRole(username, newRole)
      this.showMessage(`用户角色更新成功`, 'success')
      await this.loadUsers()
    },

    // 切换用户状态
    toggleUserStatus(user) {
      const action = user.status === 'active' ? '禁用' : '启用'
      const newStatus = user.status === 'active' ? 'banned' : 'active'

      this.confirmTitle = `${action}用户账户`
      this.confirmMessage = `确定要${action}用户 "${user.username}" 吗？`
      this.confirmType = user.status === 'active' ? 'error' : 'success'
      this.confirmButtonText = `确认${action}`

      if (user.status === 'active') {
        this.confirmAlertMessage = `
          <strong>禁用用户影响：</strong><br>
          • 用户将无法登录系统<br>
          • 现有会话将被终止<br>
          • 用户数据保持完整<br>
          • 可随时重新启用<br>
          <br>
          <strong>📧 建议：</strong>禁用前通知用户原因
        `
      } else {
        this.confirmAlertMessage = `
          <strong>启用用户效果：</strong><br>
          • 用户可以正常登录<br>
          • 恢复所有使用权限<br>
          • 数据访问权限恢复<br>
          <br>
          <strong>✅ 确认：</strong>该用户可以重新使用系统
        `
      }

      this.confirmAction = () => this.executeStatusChange(user.username, newStatus)
      this.confirmDialog = true
    },

    // 执行状态变更
    async executeStatusChange(username, newStatus) {
      await userApi.updateUserStatus(username, newStatus)
      this.showMessage(`用户状态更新成功`, 'success')
      await this.loadUsers()
    },

    // 重置密码
    resetPassword(user) {
      this.confirmTitle = '重置用户密码'
      this.confirmMessage = `确定要重置用户 "${user.username}" 的密码吗？`
      this.confirmType = 'warning'
      this.confirmButtonText = '确认重置'
      this.confirmAlertMessage = `
        <strong>密码重置流程：</strong><br>
        • 系统将生成8位随机临时密码<br>
        • 用户当前密码将立即失效<br>
        • 需要通过安全渠道告知用户新密码<br>
        • 建议用户首次登录后立即修改<br>
        <br>
        <strong>🔒 安全提醒：</strong><br>
        • 请确保安全传达新密码<br>
        • 避免通过不安全渠道发送<br>
        • 记录操作时间和原因
      `
      this.confirmAction = () => this.executePasswordReset(user.username)
      this.confirmDialog = true
    },

    // 执行密码重置
    async executePasswordReset(username) {
      const response = await userApi.resetPassword(username)
      console.log('重置密码响应:', response) // 调试日志
      this.newPasswordInfo = {
        username: username,
        newPassword: response.newPassword // 修复：直接访问response.newPassword
      }
      this.resetPasswordDialog = true
      this.showMessage('密码重置成功', 'success')
    },

    // 关闭重置密码结果对话框
    closeResetPasswordDialog() {
      this.resetPasswordDialog = false
      this.newPasswordInfo = {
        username: '',
        newPassword: ''
      }
    },

    // 复制密码到剪贴板
    async copyPassword() {
      try {
        await navigator.clipboard.writeText(this.newPasswordInfo.newPassword)
        this.showMessage('密码已复制到剪贴板', 'success')
      } catch (error) {
        console.error('复制失败:', error)
        this.showMessage('复制失败，请手动复制', 'error')
      }
    },



    showMessage(message, color = 'success') {
      this.snackbarMessage = message
      this.snackbarColor = color
      this.snackbar = true
    }
  }
}
</script>

<style scoped>
.user-management-page {
  background-color: #F5F5F5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.operation-btn {
  min-width: 120px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.gap-2 {
  gap: 8px;
}

/* 背景渐变 */
.bg-gradient {
  background: linear-gradient(135deg, #1976d2, #42a5f5) !important;
}

.white--text {
  color: white !important;
}

/* 统计卡片样式 */
.v-card.v-theme--light.v-card--variant-tonal {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.v-card.v-theme--light.v-card--variant-tonal:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* 数据表格样式 */
.v-data-table {
  border-radius: 8px !important;
}

.v-data-table .v-data-table__thead {
  background-color: rgba(25, 118, 210, 0.05);
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}
</style>
