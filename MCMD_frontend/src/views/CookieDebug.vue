<template>
  <v-container>
    <v-card>
      <v-card-title>Cookie认证调试</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12">
            <h3>Cookie状态</h3>
            <pre>{{ cookieInfo }}</pre>
          </v-col>
        </v-row>
        
        <v-row>
          <v-col cols="12">
            <v-btn @click="refreshInfo" color="primary">刷新信息</v-btn>
            <v-btn @click="testSSE" color="success" class="ml-2">测试SSE</v-btn>
          </v-col>
        </v-row>
        
        <v-row v-if="testResult">
          <v-col cols="12">
            <v-alert :type="testResult.type">{{ testResult.message }}</v-alert>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  name: 'CookieDebug',
  data() {
    return {
      cookieInfo: '',
      testResult: null,
      testEventSource: null,
      testTimeout: null
    }
  },
  
  mounted() {
    this.refreshInfo()
  },
  
  methods: {
    refreshInfo() {
      const info = []
      info.push('Cookie: ' + (document.cookie || '(无)'))
      info.push('')
      
      const username = sessionStorage.getItem('username')
      info.push('登录状态: ' + (username ? `已登录(${username})` : '未登录'))
      
      this.cookieInfo = info.join('\n')
    },
    
    testSSE() {
      this.testResult = { type: 'info', message: '测试中...' }

      // 安全清理之前的EventSource
      this.cleanupEventSource()

      try {
        this.testEventSource = new EventSource('/api/materials/MCMD-1/analyze')

        // 添加完整的事件处理
        this.testEventSource.onopen = () => {
          console.log('SSE连接已建立')
        }

        this.testEventSource.onmessage = (event) => {
          console.log('收到SSE消息:', event.data)
        }

        this.testEventSource.onerror = (error) => {
          console.error('SSE连接错误:', error)
          this.testResult = { type: 'error', message: '连接失败，检查后端日志' }
          this.cleanupEventSource()
        }

        // 设置超时清理
        this.testTimeout = setTimeout(() => {
          this.cleanupEventSource()
          this.testResult = { type: 'success', message: '测试完成，检查后端日志' }
        }, 2000)

      } catch (error) {
        console.error('创建EventSource失败:', error)
        this.testResult = { type: 'error', message: '创建连接失败: ' + error.message }
      }
    },

    // 安全的EventSource清理方法
    cleanupEventSource() {
      if (this.testEventSource) {
        try {
          this.testEventSource.onopen = null
          this.testEventSource.onmessage = null
          this.testEventSource.onerror = null
          this.testEventSource.onclose = null

          if (this.testEventSource.readyState !== EventSource.CLOSED) {
            this.testEventSource.close()
          }
        } catch (error) {
          console.warn('清理EventSource时出错:', error)
        } finally {
          this.testEventSource = null
        }
      }

      if (this.testTimeout) {
        clearTimeout(this.testTimeout)
        this.testTimeout = null
      }
    }
  },

  beforeUnmount() {
    // 使用统一的清理方法
    this.cleanupEventSource()
  },
}
</script>
