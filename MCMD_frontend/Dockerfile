# 使用官方的 Nginx 镜像作为基础镜像
FROM nginx:alpine

# 删除默认的 Nginx 配置文件
RUN rm /etc/nginx/conf.d/default.conf

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 将SSL证书复制到容器中
COPY mcmd.ac.cn_nginx/mcmd.ac.cn_bundle.crt /etc/nginx/ssl/
COPY mcmd.ac.cn_nginx/mcmd.ac.cn.key /etc/nginx/ssl/

# 将生产环境的 Nginx 配置文件复制到容器中
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# 将构建好的前端静态文件复制到 Nginx 的默认静态文件目录
COPY dist/ /usr/share/nginx/html

# 暴露 80 和 443 端口
EXPOSE 80 443

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
