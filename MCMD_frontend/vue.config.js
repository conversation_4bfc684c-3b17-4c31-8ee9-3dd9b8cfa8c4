module.exports = {
  transpileDependencies: ['vuetify'],
  lintOnSave: false,
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        },
        // 支持Server-Sent Events
        onProxyReq: (proxyReq, req, res) => {
          if (req.headers.accept && req.headers.accept.includes('text/event-stream')) {
            proxyReq.setHeader('Cache-Control', 'no-cache');
            proxyReq.setHeader('Connection', 'keep-alive');
          }
        },
        onProxyRes: (proxyRes, req, res) => {
          if (proxyRes.headers['content-type'] && proxyRes.headers['content-type'].includes('text/event-stream')) {
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            res.setHeader('Content-Type', 'text/event-stream');
          }
        }
      }
    },
    client: {
      overlay: false // 禁用错误遮罩，提高性能
    }
  },
  // 优化构建性能
  productionSourceMap: false,
  // 配置 Webpack
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 20000
      }
    },
    resolve: {
      fallback: {
        crypto: false
      }
    },
    plugins: [
      new (require('webpack')).DefinePlugin({
        // Vue 3 feature flags
        __VUE_OPTIONS_API__: JSON.stringify(true),
        __VUE_PROD_DEVTOOLS__: JSON.stringify(false),
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false)
      })
    ]
  }
}
