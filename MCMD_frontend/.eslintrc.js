module.exports = {
  root: true,
  env: {
    node: true,
    'vue/setup-compiler-macros': true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: '@babel/eslint-parser',
    requireConfigFile: false
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/multi-word-component-names': 'off',
    // 完全关闭 v-slot 验证规则，因为 Vuetify 数据表格使用特殊语法
    'vue/valid-v-slot': 'off',
    // 允许未使用的变量（如果它们是以下划线开头的）
    'no-unused-vars': ['error', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    // 关闭对 v-for template key 的检查
    'vue/no-v-for-template-key': 'off',
    // 关闭其他可能冲突的 Vue 规则
    'vue/no-v-for-template-key-on-child': 'off'
  },
  // 为特定文件覆盖规则
  overrides: [
    {
      files: ['src/components/SearchResults.vue'],
      rules: {
        'vue/valid-v-slot': 'off'
      }
    }
  ]
}
