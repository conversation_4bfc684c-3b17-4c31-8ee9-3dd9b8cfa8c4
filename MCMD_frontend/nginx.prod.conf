# 拒绝IP访问 - HTTP
server {
    listen       80 default_server;
    server_name  _;
    return       444;  # 直接关闭连接
}

# HTTP服务器 - 重定向到HTTPS
server {
    listen       80;
    server_name  mcmd.ac.cn;

    # 将所有HTTP请求重定向到HTTPS
    return 301 https://mcmd.ac.cn$request_uri;
}

# 拒绝IP访问 - HTTPS
server {
    listen       443 ssl default_server;
    http2        on;
    server_name  _;
    
    # 使用SSL证书
    ssl_certificate /etc/nginx/ssl/mcmd.ac.cn_bundle.crt;
    ssl_certificate_key /etc/nginx/ssl/mcmd.ac.cn.key;
    
    return       444;  # 直接关闭连接
}

# HTTPS服务器
server {
    listen       443 ssl;
    http2        on;
    server_name  mcmd.ac.cn;

    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/mcmd.ac.cn_bundle.crt;
    ssl_certificate_key /etc/nginx/ssl/mcmd.ac.cn.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 根目录指向静态文件
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri /index.html;  # 支持前端路由
    }

    # SSE (Server-Sent Events) 专用配置 - AI分析接口
    location ~ ^/api/materials/([^/]+)/analyze$ {
        # 处理CORS预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://mcmd.ac.cn' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 3600 always;
            add_header 'Content-Length' 0;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            return 204;
        }

        proxy_pass http://*************:8081/materials/$1/analyze;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin https://mcmd.ac.cn;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_hide_header X-Powered-By;

        # SSE专用配置 - 禁用缓冲和压缩
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;

        # 确保SSE响应头正确传递
        proxy_set_header Accept text/event-stream;
        proxy_set_header Cache-Control no-cache;

        # 添加CORS响应头部
        add_header 'Access-Control-Allow-Origin' 'https://mcmd.ac.cn' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'X-Accel-Buffering' 'no' always;
    }

    # API 转发规则 - 普通API请求
    location /api/ {
        # 处理CORS预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://mcmd.ac.cn' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 3600 always;
            add_header 'Content-Length' 0;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            return 204;
        }

        proxy_pass http://*************:8081/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin https://mcmd.ac.cn;  # 设置正确的Origin
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_hide_header X-Powered-By;

        # 添加CORS响应头部
        add_header 'Access-Control-Allow-Origin' 'https://mcmd.ac.cn' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        proxy_buffering on;
    }

    # 错误页面配置
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
