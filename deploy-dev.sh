#!/bin/bash

# MCMD项目开发环境部署脚本
# 作者: <PERSON>
# 日期: 2025-07-02

set -e  # 遇到错误立即退出

echo "🚀 开始部署MCMD项目到开发环境..."

# 检查必要文件
echo "📋 检查部署前置条件..."

if [ ! -f "MCMD_backend/.env" ]; then
    echo "❌ 错误: MCMD_backend/.env 文件不存在"
    echo "请复制 MCMD_backend/.env.example 并配置正确的环境变量"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker stop mcmd_backend mcmd_frontend 2>/dev/null || true
docker rm mcmd_backend mcmd_frontend 2>/dev/null || true

# 构建后端
echo "🔨 构建后端应用..."
cd MCMD_backend

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: Maven未安装，请先安装Maven"
    exit 1
fi

# 构建JAR文件（包含Python脚本支持）
echo "📦 编译Java应用..."
mvn clean package -DskipTests

# 检查Python依赖
echo "🐍 检查Python依赖..."
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: requirements.txt 文件不存在"
    exit 1
fi

# 构建Docker镜像（包含Java + Python环境）
echo "🐳 构建后端Docker镜像（Java + Python）..."
docker build -t mcmd_backend .

# 启动后端容器（开发环境）
echo "🚀 启动后端容器（开发环境）..."
docker run -d \
    --name mcmd_backend \
    --env-file .env \
    -e CORS_ALLOWED_ORIGINS="http://localhost:8080,http://127.0.0.1:8080" \
    -e COOKIE_SECURE="false" \
    -e COOKIE_DOMAIN="" \
    -p 8081:8081 \
    --restart unless-stopped \
    mcmd_backend

cd ..

# 构建前端
echo "🔨 构建前端应用..."
cd MCMD_frontend

# 检查Node.js是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: Node.js/npm未安装，请先安装Node.js"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 构建前端
echo "📦 构建前端应用..."
npm run build

# 使用开发环境Nginx配置（HTTP）
cp nginx.conf nginx.conf.bak 2>/dev/null || true

# 构建前端Docker镜像
echo "🐳 构建前端Docker镜像..."
docker build -t mcmd_frontend .

# 启动前端容器（开发环境 - HTTP）
echo "🚀 启动前端容器（开发环境 - HTTP）..."
docker run -d \
    --name mcmd_frontend \
    -p 8080:80 \
    --restart unless-stopped \
    mcmd_frontend

cd ..

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 执行健康检查..."

# 检查后端
if curl -f http://localhost:8081/actuator/health > /dev/null 2>&1; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    docker logs mcmd_backend --tail 20
    exit 1
fi

# 检查前端
if curl -f http://localhost:8080/ > /dev/null 2>&1; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    docker logs mcmd_frontend --tail 20
    exit 1
fi

echo ""
echo "🎉 开发环境部署完成！"
echo ""
echo "📊 服务状态:"
echo "- 后端API: http://localhost:8081"
echo "- 前端网站: http://localhost:8080"
echo ""
echo "📝 查看日志:"
echo "- 后端日志: docker logs mcmd_backend"
echo "- 前端日志: docker logs mcmd_frontend"
echo ""
echo "🔧 管理命令:"
echo "- 重启后端: docker restart mcmd_backend"
echo "- 重启前端: docker restart mcmd_frontend"
echo "- 停止所有: docker stop mcmd_backend mcmd_frontend"
