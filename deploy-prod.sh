#!/bin/bash

# MCMD项目生产环境部署脚本
# 作者: <PERSON>
# 日期: 2025-07-02

set -e  # 遇到错误立即退出

echo "🚀 开始部署MCMD项目到生产环境..."

# 检查必要文件
echo "📋 检查部署前置条件..."

if [ ! -f "MCMD_backend/.env" ]; then
    echo "❌ 错误: MCMD_backend/.env 文件不存在"
    echo "请复制 MCMD_backend/.env.example 并配置正确的环境变量"
    exit 1
fi

if [ ! -d "MCMD_frontend/dist" ]; then
    echo "❌ 错误: 前端构建文件不存在"
    echo "请先运行: cd MCMD_frontend && npm run build"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker stop mcmd_backend mcmd_frontend_https 2>/dev/null || true
docker rm mcmd_backend mcmd_frontend_https 2>/dev/null || true

# 构建后端
echo "🔨 构建后端应用..."
cd MCMD_backend

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: Maven未安装，请先安装Maven"
    exit 1
fi

# 构建JAR文件（包含Python脚本支持）
echo "📦 编译Java应用..."
mvn clean package -DskipTests

# 检查Python依赖
echo "🐍 检查Python依赖..."
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: requirements.txt 文件不存在"
    exit 1
fi

# 构建Docker镜像（包含Java + Python环境）
echo "🐳 构建后端Docker镜像（Java + Python）..."
docker build -t mcmd_backend .

# 启动后端容器
echo "🚀 启动后端容器..."
docker run -d \
    --name mcmd_backend \
    --env-file .env \
    -p 8081:8081 \
    --restart unless-stopped \
    mcmd_backend

cd ..

# 构建前端
echo "🔨 构建前端应用..."
cd MCMD_frontend

# 检查SSL证书
if [ ! -f "mcmd.ac.cn_nginx/mcmd.ac.cn_bundle.crt" ] || [ ! -f "mcmd.ac.cn_nginx/mcmd.ac.cn.key" ]; then
    echo "❌ 错误: SSL证书文件不存在"
    echo "请将SSL证书文件放置在 MCMD_frontend/mcmd.ac.cn_nginx/ 目录下"
    exit 1
fi

# 使用生产环境Nginx配置
cp nginx.prod.conf nginx.conf

# 构建前端Docker镜像
echo "🐳 构建前端Docker镜像..."
docker build -t mcmd_frontend_https .

# 启动前端容器
echo "🚀 启动前端容器..."
docker run -d \
    --name mcmd_frontend_https \
    -p 80:80 \
    -p 443:443 \
    --restart unless-stopped \
    mcmd_frontend_https

cd ..

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 执行健康检查..."

# 检查后端 (使用根路径而不是actuator/health)
if curl -s http://localhost:8081/ | grep -q "Method not supported"; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    docker logs mcmd_backend --tail 20
    exit 1
fi

# 检查前端HTTPS (使用域名Host头)
if curl -k -s -H "Host: mcmd.ac.cn" https://localhost/ | grep -q "MCMD"; then
    echo "✅ 前端HTTPS服务启动成功"
else
    echo "❌ 前端HTTPS服务启动失败"
    docker logs mcmd_frontend_https --tail 20
    exit 1
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📊 服务状态:"
echo "- 后端API: http://localhost:8081"
echo "- 前端网站: https://mcmd.ac.cn"
echo ""
echo "📝 查看日志:"
echo "- 后端日志: docker logs mcmd_backend"
echo "- 前端日志: docker logs mcmd_frontend_https"
echo ""
echo "🔧 管理命令:"
echo "- 重启后端: docker restart mcmd_backend"
echo "- 重启前端: docker restart mcmd_frontend_https"
echo "- 停止所有: docker stop mcmd_backend mcmd_frontend_https"
